using UnityEngine;
using BTR.Player;

namespace BTR.Player
{
    /// <summary>
    /// Integration between player system and the global audio system.
    /// Manages music parameters and global audio state based on player events.
    /// Does NOT handle individual audio events (PlayerAudioComponent handles those).
    /// </summary>
    public class AudioSystemIntegration : PlayerSystemIntegrationBase
    {
        private AudioManager audioManager;
        private PlayerEntity currentPlayer;

        protected override void OnInitialize()
        {
            // Get AudioManager instance
            audioManager = AudioManager.Instance;

            if (audioManager == null)
            {
                Debug.LogWarning("[AudioSystemIntegration] AudioManager not found");
                return;
            }

            Debug.Log("[AudioSystemIntegration] Initialized");
        }

        protected override void OnCleanup()
        {
            UnsubscribeFromPlayerEvents();
            currentPlayer = null;

            Debug.Log("[AudioSystemIntegration] Cleaned up");
        }

        protected override void HandlePlayerSpawned(PlayerEntity player)
        {
            currentPlayer = player;
            SubscribeToPlayerEvents();

            // Set initial music parameters for player spawn
            if (audioManager != null)
            {
                audioManager.SetMusicParameter("Player_State", 1f); // Player active
                audioManager.SetMusicParameter("Combat_State", 0f); // Not in combat
                audioManager.SetMusicParameter("Time_State", 0f); // Normal time
            }

            Debug.Log("[AudioSystemIntegration] Player spawned - music parameters initialized");
        }

        protected override void HandlePlayerDestroyed(PlayerEntity player)
        {
            UnsubscribeFromPlayerEvents();

            // Set music state for player destruction
            if (audioManager != null)
            {
                audioManager.SetMusicParameter("Player_State", 0f); // Player inactive
                audioManager.SetMusicParameter("Combat_State", 0f); // Reset combat state
                audioManager.SetMusicParameter("Time_State", 0f); // Reset time state
            }

            currentPlayer = null;

            Debug.Log("[AudioSystemIntegration] Player destroyed - music parameters reset");
        }

        protected override void HandlePlayerStateChanged(PlayerState oldState, PlayerState newState)
        {
            if (audioManager == null)
                return;

            // Update music parameters based on player state
            switch (newState)
            {
                case PlayerState.Dead:
                    audioManager.SetMusicParameter("Player_State", 0f);
                    audioManager.SetMusicParameter("Combat_State", 0f);
                    audioManager.SetMusicParameter("Time_State", 0f);
                    break;

                case PlayerState.Active:
                    audioManager.SetMusicParameter("Player_State", 1f);
                    break;

                case PlayerState.Combat:
                    audioManager.SetMusicParameter("Combat_State", 1f);
                    break;

                case PlayerState.TimeControl:
                    HandleTimeControlMusicParameters();
                    break;
            }

            // Reset combat state when leaving combat
            if (oldState == PlayerState.Combat && newState != PlayerState.Combat)
            {
                audioManager.SetMusicParameter("Combat_State", 0f);
            }

            // Reset time state when leaving time control
            if (oldState == PlayerState.TimeControl && newState != PlayerState.TimeControl)
            {
                audioManager.SetMusicParameter("Time_State", 0f);
            }

            Debug.Log($"[AudioSystemIntegration] Music parameters updated for state change: {oldState} -> {newState}");
        }

        private void SubscribeToPlayerEvents()
        {
            if (currentPlayer == null)
                return;

            // Subscribe to health component events for music parameter updates
            var healthComponent = currentPlayer.GetComponent<PlayerHealthComponent>();
            if (healthComponent != null)
            {
                healthComponent.OnHealthChanged += OnPlayerHealthChanged;
            }
        }

        private void UnsubscribeFromPlayerEvents()
        {
            if (currentPlayer == null)
                return;

            // Unsubscribe from health component events
            var healthComponent = currentPlayer.GetComponent<PlayerHealthComponent>();
            if (healthComponent != null)
            {
                healthComponent.OnHealthChanged -= OnPlayerHealthChanged;
            }
        }

        private void HandleTimeControlMusicParameters()
        {
            if (audioManager == null || currentPlayer == null)
                return;

            var timeControlComponent = currentPlayer.GetComponent<PlayerTimeControlComponent>();
            if (timeControlComponent == null)
                return;

            if (timeControlComponent.IsRewinding())
            {
                audioManager.SetMusicParameter("Time_State", 1f); // Rewind
            }
            else if (timeControlComponent.IsSlowingTime())
            {
                audioManager.SetMusicParameter("Time_State", 2f); // Slow time
            }
        }

        #region Event Handlers

        private void OnPlayerHealthChanged(float newHealth)
        {
            if (currentPlayer == null || audioManager == null)
                return;

            var healthComponent = currentPlayer.GetComponent<PlayerHealthComponent>();
            if (healthComponent != null)
            {
                float healthPercentage = healthComponent.GetHealthPercentage();

                // Update music intensity based on health percentage
                audioManager.SetMusicParameter("Health", healthPercentage);
            }
        }

        #endregion

        #region Public API

        /// <summary>
        /// Set a music parameter for player-related audio
        /// </summary>
        /// <param name="parameterName">Parameter name</param>
        /// <param name="value">Parameter value</param>
        public void SetMusicParameter(string parameterName, float value)
        {
            if (audioManager != null)
            {
                audioManager.SetMusicParameter(parameterName, value);
            }
        }

        /// <summary>
        /// Get the current AudioManager instance
        /// </summary>
        /// <returns>AudioManager instance or null if not available</returns>
        public AudioManager GetAudioManager()
        {
            return audioManager;
        }

        #endregion
    }
}
