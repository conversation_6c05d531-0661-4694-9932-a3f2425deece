using System.Collections;
using UnityEngine;
using SensorToolkit;
using Lofelt.NiceVibrations;
using BTR.Player;
using BTR.Projectiles;

namespace BTR.Player
{
    /// <summary>
    /// Handles player dodge/ricochet mechanics.
    /// Extracted from PlayerMovement.cs - handles dodge state and projectile ricochet.
    /// </summary>
    public class PlayerDodgeComponent : PlayerComponent, IInputPlayerComponent, IStatefulPlayerComponent
    {
        [Header("Dodge Settings")]
        [SerializeField] private float dodgeDuration = 0.5f;
        [SerializeField] private float dodgeCooldown = 1f;
        [SerializeField] private float dodgeDistance = 5f;
        [SerializeField] private bool enableInvulnerability = true;

        [Header("Effects")]
        [SerializeField] private GameObject ricochetBlast;
        [SerializeField] private GameObject ricochetFeedbackObject;
        [SerializeField] private bool enableHapticFeedback = true;
        [SerializeField] private bool enableAudioFeedback = true;

        [Header("Ricochet Detection")]
        [SerializeField] private bool enableProjectileRicochet = true;
        [SerializeField] private LayerMask projectileLayers = 1 << 13;

        // Component references
        private Rigidbody playerRigidbody;
        private RangeSensor rangeSensor;
        private StaminaController staminaController;
        private MovementConfiguration movementConfig;

        // Dodge state
        private bool isDodging;
        private float lastDodgeTime = -Mathf.Infinity;
        private Coroutine dodgeCoroutine;

        // Input handling
        public bool ShouldReceiveInput => IsEnabled && !isDodging;
        private bool dodgePressed;

        #region PlayerComponent Overrides

        protected override void OnComponentInitialize()
        {
            InitializeReferences();
            LoadConfiguration();
        }

        protected override void OnComponentStarted()
        {
            // Initialize dodge state
            isDodging = false;
            lastDodgeTime = -Mathf.Infinity;
        }

        public override void OnComponentUpdate()
        {
            HandleDodgeInput();
        }

        protected override bool OnComponentValidate()
        {
            if (playerRigidbody == null)
            {
                LogError("Rigidbody component is required for dodge");
                return false;
            }

            return true;
        }

        protected override void OnComponentStopped()
        {
            // Stop any ongoing dodge
            if (dodgeCoroutine != null)
            {
                StopCoroutine(dodgeCoroutine);
                dodgeCoroutine = null;
            }

            isDodging = false;
        }

        #endregion

        #region Initialization

        private void InitializeReferences()
        {
            playerRigidbody = GetComponent<Rigidbody>();
            rangeSensor = GetComponent<RangeSensor>();
            staminaController = GetComponent<StaminaController>();
        }

        private void LoadConfiguration()
        {
            var playerCore = GetComponent<PlayerCore>();
            if (playerCore != null && playerCore.Configuration != null)
            {
                movementConfig = playerCore.Configuration.MovementConfig;
                ApplyConfiguration();
            }
        }

        private void ApplyConfiguration()
        {
            if (movementConfig != null)
            {
                dodgeDuration = movementConfig.DodgeDuration;
                dodgeCooldown = movementConfig.DodgeCooldown;
                dodgeDistance = movementConfig.DodgeDistance;
                enableInvulnerability = movementConfig.DodgeInvulnerability;
            }
        }

        #endregion

        #region Input Handling

        public void HandleInput(PlayerInputData inputData)
        {
            if (!ShouldReceiveInput)
                return;

            dodgePressed = inputData.dodge;
        }

        private void HandleDodgeInput()
        {
            if (dodgePressed && CanDodge())
            {
                TryRicochetDodge();
            }
        }

        #endregion

        #region Dodge Logic

        private bool CanDodge()
        {
            if (isDodging)
                return false;

            if (Time.time - lastDodgeTime < dodgeCooldown)
                return false;

            // Check stamina if available - we'll check if we can use stamina by checking current percentage
            if (staminaController != null)
            {
                // Estimate if we have enough stamina (assuming 20% cost per dodge)
                float currentStaminaPercentage = staminaController.GetCurrentStaminaPercentage();
                if (currentStaminaPercentage < 0.2f) // Less than 20% stamina
                    return false;
            }

            return true;
        }

        private void TryRicochetDodge()
        {
            if (!CanDodge())
                return;

            // Consume stamina if available
            if (staminaController != null && !staminaController.TryUseDodgeStamina())
                return;

            dodgeCoroutine = StartCoroutine(PerformRicochetDodge());
        }

        private IEnumerator PerformRicochetDodge()
        {
            // Start dodge
            isDodging = true;
            lastDodgeTime = Time.time;

            // Notify player entity of state change
            if (PlayerEntity != null)
            {
                // PlayerEntity will handle state change to Dodging
            }

            // Calculate dodge direction and position
            Vector3 dodgeDirection = transform.forward;
            Vector3 startPosition = transform.position;
            Vector3 dodgePosition = startPosition + dodgeDirection * dodgeDistance;

            // Play effects
            PlayDodgeEffects();

            // Handle projectile ricochet
            if (enableProjectileRicochet)
            {
                RicochetNearbyProjectiles();
            }

            // Perform dodge movement
            float elapsedTime = 0f;

            while (elapsedTime < dodgeDuration)
            {
                float progress = elapsedTime / dodgeDuration;
                Vector3 newPosition = Vector3.Lerp(startPosition, dodgePosition, progress);

                if (playerRigidbody != null)
                {
                    playerRigidbody.MovePosition(newPosition);
                }

                elapsedTime += Time.deltaTime;
                yield return null;
            }

            // Ensure final position
            if (playerRigidbody != null)
            {
                playerRigidbody.MovePosition(dodgePosition);
            }

            // End dodge
            isDodging = false;
            dodgeCoroutine = null;

            LogDebug("Dodge completed");
        }

        #endregion

        #region Effects

        private void PlayDodgeEffects()
        {
            // Audio feedback - trigger GameEvents
            if (enableAudioFeedback)
            {
                if (GameEventsManager.Instance?.Events != null)
                {
                    GameEventsManager.Instance.Events.TriggerPlayerDodge();
                }
                else
                {
                    // Fallback to direct FMOD call
                    FMODUnity.RuntimeManager.PlayOneShot("event:/Player/Ricochet");
                }
            }

            // Haptic feedback
            if (enableHapticFeedback)
            {
                HapticPatterns.PlayEmphasis(1.0f, 0.0f);
            }

            // Visual effects
            if (ricochetBlast != null)
            {
                ricochetBlast.SetActive(true);
                StartCoroutine(DisableRicochetBlast());
            }

            if (ricochetFeedbackObject != null)
            {
                // Trigger feedback object if it has a feedback component
                var feedback = ricochetFeedbackObject.GetComponent<MoreMountains.Feedbacks.MMF_Player>();
                if (feedback != null)
                {
                    feedback.PlayFeedbacks();
                }
            }
        }

        private IEnumerator DisableRicochetBlast()
        {
            yield return new WaitForSeconds(dodgeDuration);

            if (ricochetBlast != null)
            {
                ricochetBlast.SetActive(false);
            }
        }

        #endregion

        #region Projectile Ricochet

        private void RicochetNearbyProjectiles()
        {
            if (rangeSensor == null || rangeSensor.DetectedObjects.Count == 0)
                return;

            foreach (var detectedObject in rangeSensor.DetectedObjects)
            {
                // Check if object is a projectile
                var projectile = detectedObject.GetComponent<IProjectile>();
                if (projectile != null)
                {
                    HandleProjectileRicochet(detectedObject);
                }
            }
        }

        private void HandleProjectileRicochet(GameObject projectileObject)
        {
            // Handle new projectile system
            var movement = projectileObject.GetComponent<ProjectileMovement>();
            if (movement != null)
            {
                movement.OnPlayerRicochetDodge();
                LogDebug($"Ricocheted projectile: {projectileObject.name}");
                return;
            }

            // Handle legacy projectile system if needed
            var legacyProjectile = projectileObject.GetComponent<IProjectile>();
            if (legacyProjectile != null)
            {
                // Legacy ricochet handling
                LogDebug($"Ricocheted legacy projectile: {projectileObject.name}");
            }
        }

        #endregion

        #region IStatefulPlayerComponent Implementation

        public void OnPlayerStateChanged(PlayerState oldState, PlayerState newState)
        {
            switch (newState)
            {
                case PlayerState.Dead:
                case PlayerState.Inactive:
                    // Stop any ongoing dodge
                    if (isDodging && dodgeCoroutine != null)
                    {
                        StopCoroutine(dodgeCoroutine);
                        dodgeCoroutine = null;
                        isDodging = false;
                    }
                    break;
            }
        }

        public bool CanTransitionToState(PlayerState targetState)
        {
            // Allow transition to dodging state
            if (targetState == PlayerState.Dodging)
            {
                return CanDodge();
            }

            // Don't restrict other transitions
            return true;
        }

        #endregion

        #region Public API

        /// <summary>
        /// Check if the player is currently dodging
        /// </summary>
        /// <returns>True if dodging</returns>
        public bool IsDodging()
        {
            return isDodging;
        }

        /// <summary>
        /// Get the time since the last dodge
        /// </summary>
        /// <returns>Time since last dodge</returns>
        public float GetTimeSinceLastDodge()
        {
            return Time.time - lastDodgeTime;
        }

        /// <summary>
        /// Check if dodge is available (not on cooldown)
        /// </summary>
        /// <returns>True if dodge is available</returns>
        public bool IsDodgeAvailable()
        {
            return CanDodge();
        }

        /// <summary>
        /// Force stop the current dodge
        /// </summary>
        public void StopDodge()
        {
            if (isDodging && dodgeCoroutine != null)
            {
                StopCoroutine(dodgeCoroutine);
                dodgeCoroutine = null;
                isDodging = false;
                LogDebug("Dodge force stopped");
            }
        }

        /// <summary>
        /// Set the ricochet blast effect object
        /// </summary>
        /// <param name="blastObject">Blast effect object</param>
        public void SetRicochetBlast(GameObject blastObject)
        {
            ricochetBlast = blastObject;
        }

        #endregion
    }
}
