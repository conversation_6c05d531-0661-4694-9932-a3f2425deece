using UnityEngine;
using BTR.Player;

namespace BTR
{
    /// <summary>
    /// Manages audio events and integrates with PlayerAudioComponent.
    /// Provides decoupled audio triggering through events.
    /// </summary>
    [DefaultExecutionOrder(-150)] // Initialize before most other managers
    public class AudioEventsManager : MonoBehaviour
    {
        private static AudioEventsManager instance;
        public static AudioEventsManager Instance => instance;

        [SerializeField] private AudioEvents audioEvents;
        public AudioEvents Events => audioEvents;

        // Domain reload handling
        [RuntimeInitializeOnLoadMethod(RuntimeInitializeLoadType.SubsystemRegistration)]
        private static void ResetStaticData()
        {
            instance = null;
        }

        private void Awake()
        {
            if (instance == null)
            {
                instance = this;
                DontDestroyOnLoad(gameObject);

                if (audioEvents == null)
                {
                    Debug.LogError($"[{nameof(AudioEventsManager)}] AudioEvents ScriptableObject not assigned! Create one via Create > BTR > Events > AudioEvents");
                }
            }
            else
            {
                // Transfer any updated references
                if (audioEvents != null)
                {
                    instance.audioEvents = audioEvents;
                }
                Destroy(gameObject);
            }
        }

        private void Start()
        {
            InitializeAudioEventSubscriptions();
        }

        private void InitializeAudioEventSubscriptions()
        {
            if (audioEvents == null)
                return;

            // Subscribe PlayerAudioComponent to audio events
            SubscribePlayerAudioEvents();

            Debug.Log($"[{GetType().Name}] Audio event subscriptions initialized");
        }

        private void SubscribePlayerAudioEvents()
        {
            // Wait for PlayerAudioComponent to be available
            StartCoroutine(WaitForPlayerAudioComponent());
        }

        private System.Collections.IEnumerator WaitForPlayerAudioComponent()
        {
            // Wait until PlayerAudioComponent singleton is available
            while (PlayerAudioReferences.Instance == null)
            {
                yield return new WaitForSeconds(0.1f);
            }

            var playerAudio = PlayerAudioReferences.Instance;

            // Subscribe to player combat events
            audioEvents.OnPlayerShoot += playerAudio.PlayShootingSound;
            audioEvents.OnPlayerReload += playerAudio.PlayReload;
            audioEvents.OnPlayerLockTarget += playerAudio.PlayLockTarget;
            audioEvents.OnPlayerDodge += playerAudio.PlayDodge;

            // Subscribe to player health events
            audioEvents.OnPlayerDamaged += (damage) => playerAudio.PlayDamage();
            audioEvents.OnPlayerDeath += playerAudio.PlayDeath;
            audioEvents.OnPlayerHealed += (healAmount) => playerAudio.PlayHeal();

            // Subscribe to player time control events
            audioEvents.OnPlayerRewindStart += playerAudio.PlayRewind;
            audioEvents.OnPlayerSlowTimeStart += playerAudio.PlaySlowTime;
            audioEvents.OnPlayerTimeRestore += playerAudio.PlayTimeRestore;

            // Subscribe to UI events
            audioEvents.OnMenuSelect += playerAudio.PlayMenuSelect;
            audioEvents.OnMenuConfirm += playerAudio.PlayMenuConfirm;
            audioEvents.OnMenuCancel += playerAudio.PlayMenuCancel;

            Debug.Log("[AudioEventsManager] PlayerAudioComponent subscriptions complete");
        }

        private void OnDestroy()
        {
            if (instance == this)
            {
                instance = null;
            }
        }

        #region Public API

        /// <summary>
        /// Get the audio events instance for triggering audio
        /// </summary>
        public static AudioEvents GetAudioEvents()
        {
            return Instance?.audioEvents;
        }

        /// <summary>
        /// Quick access method to trigger player shooting audio
        /// </summary>
        public static void TriggerPlayerShoot()
        {
            Instance?.audioEvents?.TriggerPlayerShoot();
        }

        /// <summary>
        /// Quick access method to trigger player damage audio
        /// </summary>
        public static void TriggerPlayerDamaged(float damage)
        {
            Instance?.audioEvents?.TriggerPlayerDamaged(damage);
        }

        /// <summary>
        /// Quick access method to trigger player dodge audio
        /// </summary>
        public static void TriggerPlayerDodge()
        {
            Instance?.audioEvents?.TriggerPlayerDodge();
        }

        /// <summary>
        /// Quick access method to trigger player lock target audio
        /// </summary>
        public static void TriggerPlayerLockTarget()
        {
            Instance?.audioEvents?.TriggerPlayerLockTarget();
        }

        #endregion
    }
}
