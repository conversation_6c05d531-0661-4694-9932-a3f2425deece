using UnityEngine;
using System;

namespace BTR
{
    /// <summary>
    /// Centralized audio events for the game.
    /// Decouples audio triggers from direct component calls.
    /// </summary>
    [CreateAssetMenu(fileName = "AudioEvents", menuName = "BTR/Events/AudioEvents")]
    public class AudioEvents : ScriptableObject
    {
        #region Player Audio Events
        
        // Combat Audio Events
        public event Action OnPlayerShoot;
        public event Action OnPlayerReload;
        public event Action OnPlayerLockTarget;
        public event Action OnPlayerDodge;
        
        // Health Audio Events  
        public event Action<float> OnPlayerDamaged; // damage amount
        public event Action OnPlayerDeath;
        public event Action<float> OnPlayerHealed; // heal amount
        
        // Time Control Audio Events
        public event Action OnPlayerRewindStart;
        public event Action OnPlayerSlowTimeStart;
        public event Action OnPlayerTimeRestore;
        
        // UI Audio Events
        public event Action OnMenuSelect;
        public event Action OnMenuConfirm;
        public event Action OnMenuCancel;
        
        #endregion
        
        #region Enemy Audio Events
        
        public event Action<Vector3> OnEnemySpawned; // spawn position
        public event Action<Vector3> OnEnemyDeath; // death position
        public event Action<Vector3, float> OnEnemyDamaged; // position, damage
        public event Action<Vector3> OnEnemyProjectileFired; // fire position
        
        #endregion
        
        #region Game Audio Events
        
        public event Action OnWaveStart;
        public event Action OnWaveComplete;
        public event Action OnGameStart;
        public event Action OnGameOver;
        public event Action<int, string> OnKillStreak; // streak count, milestone name
        
        #endregion
        
        #region Trigger Methods - Player Combat
        
        public void TriggerPlayerShoot() => OnPlayerShoot?.Invoke();
        public void TriggerPlayerReload() => OnPlayerReload?.Invoke();
        public void TriggerPlayerLockTarget() => OnPlayerLockTarget?.Invoke();
        public void TriggerPlayerDodge() => OnPlayerDodge?.Invoke();
        
        #endregion
        
        #region Trigger Methods - Player Health
        
        public void TriggerPlayerDamaged(float damage) => OnPlayerDamaged?.Invoke(damage);
        public void TriggerPlayerDeath() => OnPlayerDeath?.Invoke();
        public void TriggerPlayerHealed(float healAmount) => OnPlayerHealed?.Invoke(healAmount);
        
        #endregion
        
        #region Trigger Methods - Player Time Control
        
        public void TriggerPlayerRewindStart() => OnPlayerRewindStart?.Invoke();
        public void TriggerPlayerSlowTimeStart() => OnPlayerSlowTimeStart?.Invoke();
        public void TriggerPlayerTimeRestore() => OnPlayerTimeRestore?.Invoke();
        
        #endregion
        
        #region Trigger Methods - UI
        
        public void TriggerMenuSelect() => OnMenuSelect?.Invoke();
        public void TriggerMenuConfirm() => OnMenuConfirm?.Invoke();
        public void TriggerMenuCancel() => OnMenuCancel?.Invoke();
        
        #endregion
        
        #region Trigger Methods - Enemy
        
        public void TriggerEnemySpawned(Vector3 position) => OnEnemySpawned?.Invoke(position);
        public void TriggerEnemyDeath(Vector3 position) => OnEnemyDeath?.Invoke(position);
        public void TriggerEnemyDamaged(Vector3 position, float damage) => OnEnemyDamaged?.Invoke(position, damage);
        public void TriggerEnemyProjectileFired(Vector3 position) => OnEnemyProjectileFired?.Invoke(position);
        
        #endregion
        
        #region Trigger Methods - Game
        
        public void TriggerWaveStart() => OnWaveStart?.Invoke();
        public void TriggerWaveComplete() => OnWaveComplete?.Invoke();
        public void TriggerGameStart() => OnGameStart?.Invoke();
        public void TriggerGameOver() => OnGameOver?.Invoke();
        public void TriggerKillStreak(int streakCount, string milestoneName) => OnKillStreak?.Invoke(streakCount, milestoneName);
        
        #endregion
        
        #region Cleanup
        
        private void OnDisable()
        {
            ClearAllSubscriptions();
        }
        
        public void ClearAllSubscriptions()
        {
            // Player Combat
            OnPlayerShoot = null;
            OnPlayerReload = null;
            OnPlayerLockTarget = null;
            OnPlayerDodge = null;
            
            // Player Health
            OnPlayerDamaged = null;
            OnPlayerDeath = null;
            OnPlayerHealed = null;
            
            // Player Time Control
            OnPlayerRewindStart = null;
            OnPlayerSlowTimeStart = null;
            OnPlayerTimeRestore = null;
            
            // UI
            OnMenuSelect = null;
            OnMenuConfirm = null;
            OnMenuCancel = null;
            
            // Enemy
            OnEnemySpawned = null;
            OnEnemyDeath = null;
            OnEnemyDamaged = null;
            OnEnemyProjectileFired = null;
            
            // Game
            OnWaveStart = null;
            OnWaveComplete = null;
            OnGameStart = null;
            OnGameOver = null;
            OnKillStreak = null;
        }
        
        #endregion
    }
}
