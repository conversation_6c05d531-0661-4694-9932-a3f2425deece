using UnityEngine;
using FMODUnity;
using FMOD.Studio;
using System.Collections.Generic;

namespace BTR
{
    /// <summary>
    /// Manages game-wide audio events and sounds.
    /// Handles UI, wave, game state, and other non-player specific audio.
    /// Functions similarly to PlayerAudioReferences but for broader game systems.
    /// </summary>
    [DefaultExecutionOrder(-100)]
    public class GameAudioReferences : MonoBehaviour
    {
        #region Singleton
        private static GameAudioReferences instance;
        public static GameAudioReferences Instance => instance;

        // Domain reload handling
        [RuntimeInitializeOnLoadMethod(RuntimeInitializeLoadType.SubsystemRegistration)]
        private static void ResetStaticData()
        {
            instance = null;
        }
        #endregion

        [Header("Audio Settings")]
        [SerializeField] private bool enableAudio = true;
        [SerializeField] private bool debugAudio = false;
        [SerializeField] private float masterVolume = 1.0f;

        [Header("UI Audio")]
        [SerializeField] private EventReference menuSelectEvent;
        [SerializeField] private EventReference menuConfirmEvent;
        [SerializeField] private EventReference menuCancelEvent;
        [SerializeField] private EventReference buttonClickEvent;
        [SerializeField] private EventReference notificationEvent;

        [Header("Wave Audio")]
        [SerializeField] private EventReference waveStartEvent;
        [SerializeField] private EventReference waveCompleteEvent;
        [SerializeField] private EventReference waveFailedEvent;
        [SerializeField] private EventReference allWavesCompleteEvent;

        [Header("Game State Audio")]
        [SerializeField] private EventReference gameStartEvent;
        [SerializeField] private EventReference gameOverEvent;
        [SerializeField] private EventReference gamePauseEvent;
        [SerializeField] private EventReference gameResumeEvent;
        [SerializeField] private EventReference levelCompleteEvent;



        [Header("Audio Pooling Settings")]
        [SerializeField] private int maxConcurrentSounds = 5;

        // Audio management
        private Dictionary<string, EventInstance> activeEvents;

        private void Awake()
        {
            if (instance == null)
            {
                instance = this;
                DontDestroyOnLoad(gameObject);
                InitializeAudioState();
            }
            else
            {
                Destroy(gameObject);
            }
        }

        private void Start()
        {
            SubscribeToGameEvents();
        }

        private void InitializeAudioState()
        {
            activeEvents = new Dictionary<string, EventInstance>();
            LogDebug("Game audio state initialized");
        }

        private void SubscribeToGameEvents()
        {
            if (GameEventsManager.Instance?.Events != null)
            {
                var gameEvents = GameEventsManager.Instance.Events;
                
                // Wave events
                gameEvents.OnWaveStarted += (wave) => PlayWaveStart();
                gameEvents.OnWaveCompleted += () => PlayWaveComplete();
                
                // Game state events
                gameEvents.OnGameStarted += () => PlayGameStart();
                gameEvents.OnGameOver += () => PlayGameOver();
                gameEvents.OnGamePause += () => PlayGamePause();
                gameEvents.OnGameResume += () => PlayGameResume();
                

                
                LogDebug("Subscribed to GameEvents for game audio responses");
            }
            else
            {
                Debug.LogWarning($"[{GetType().Name}] GameEventsManager not available - game audio may not play");
            }
        }

        private void OnDestroy()
        {
            UnsubscribeFromGameEvents();
            StopAllAudio();
            
            if (instance == this)
            {
                instance = null;
            }
        }

        private void UnsubscribeFromGameEvents()
        {
            if (GameEventsManager.Instance?.Events != null)
            {
                var gameEvents = GameEventsManager.Instance.Events;
                
                // Wave events
                gameEvents.OnWaveStarted -= (wave) => PlayWaveStart();
                gameEvents.OnWaveCompleted -= () => PlayWaveComplete();
                
                // Game state events
                gameEvents.OnGameStarted -= () => PlayGameStart();
                gameEvents.OnGameOver -= () => PlayGameOver();
                gameEvents.OnGamePause -= () => PlayGamePause();
                gameEvents.OnGameResume -= () => PlayGameResume();
                

            }
        }

        #region UI Audio Methods

        public void PlayMenuSelect()
        {
            PlayOneShot(menuSelectEvent);
        }

        public void PlayMenuConfirm()
        {
            PlayOneShot(menuConfirmEvent);
        }

        public void PlayMenuCancel()
        {
            PlayOneShot(menuCancelEvent);
        }

        public void PlayButtonClick()
        {
            PlayOneShot(buttonClickEvent);
        }

        public void PlayNotification()
        {
            PlayOneShot(notificationEvent);
        }

        #endregion

        #region Wave Audio Methods

        public void PlayWaveStart()
        {
            PlayOneShot(waveStartEvent);
            LogDebug("Played wave start sound");
        }

        public void PlayWaveComplete()
        {
            PlayOneShot(waveCompleteEvent);
            LogDebug("Played wave complete sound");
        }

        public void PlayWaveFailed()
        {
            PlayOneShot(waveFailedEvent);
            LogDebug("Played wave failed sound");
        }

        public void PlayAllWavesComplete()
        {
            PlayOneShot(allWavesCompleteEvent);
            LogDebug("Played all waves complete sound");
        }

        #endregion

        #region Game State Audio Methods

        public void PlayGameStart()
        {
            PlayOneShot(gameStartEvent);
            LogDebug("Played game start sound");
        }

        public void PlayGameOver()
        {
            PlayOneShot(gameOverEvent);
            LogDebug("Played game over sound");
        }

        public void PlayGamePause()
        {
            PlayOneShot(gamePauseEvent);
            LogDebug("Played game pause sound");
        }

        public void PlayGameResume()
        {
            PlayOneShot(gameResumeEvent);
            LogDebug("Played game resume sound");
        }

        public void PlayLevelComplete()
        {
            PlayOneShot(levelCompleteEvent);
            LogDebug("Played level complete sound");
        }

        #endregion





        #region FMOD Helper Methods

        private void PlayOneShot(EventReference eventRef)
        {
            if (!enableAudio || eventRef.IsNull)
                return;

            try
            {
                // Play as 2D sound since game audio is typically UI/global feedback
                var instance = RuntimeManager.CreateInstance(eventRef);
                if (instance.isValid())
                {
                    instance.setVolume(masterVolume);
                    instance.start();
                    instance.release(); // Auto-release when finished
                }
            }
            catch (System.Exception e)
            {
                Debug.LogError($"[{GetType().Name}] Error playing game audio: {e.Message}");
            }
        }

        private void StopAllAudio()
        {
            foreach (var kvp in activeEvents)
            {
                if (kvp.Value.isValid())
                {
                    kvp.Value.stop(FMOD.Studio.STOP_MODE.IMMEDIATE);
                    kvp.Value.release();
                }
            }
            activeEvents.Clear();
            LogDebug("Stopped all game audio events");
        }

        #endregion

        #region Debug Methods

        private void LogDebug(string message)
        {
            if (debugAudio)
            {
                Debug.Log($"[{GetType().Name}] {message}");
            }
        }

        [ContextMenu("Test UI Sounds")]
        private void TestUISounds()
        {
            PlayMenuSelect();
            PlayButtonClick();
            PlayNotification();
        }

        [ContextMenu("Test Wave Sounds")]
        private void TestWaveSounds()
        {
            PlayWaveStart();
            PlayWaveComplete();
        }

        [ContextMenu("Test Game State Sounds")]
        private void TestGameStateSounds()
        {
            PlayGameStart();
            PlayLevelComplete();
        }



        [ContextMenu("Validate Audio Events")]
        private void ValidateAudioEvents()
        {
            LogDebug("Validating game audio events...");
            
            // UI Audio validation
            if (menuSelectEvent.IsNull) Debug.LogWarning($"[{GetType().Name}] Menu select event not assigned");
            if (buttonClickEvent.IsNull) Debug.LogWarning($"[{GetType().Name}] Button click event not assigned");
            
            // Wave Audio validation
            if (waveStartEvent.IsNull) Debug.LogWarning($"[{GetType().Name}] Wave start event not assigned");
            if (waveCompleteEvent.IsNull) Debug.LogWarning($"[{GetType().Name}] Wave complete event not assigned");
            
            // Game State validation
            if (gameStartEvent.IsNull) Debug.LogWarning($"[{GetType().Name}] Game start event not assigned");
            if (gameOverEvent.IsNull) Debug.LogWarning($"[{GetType().Name}] Game over event not assigned");
            if (levelCompleteEvent.IsNull) Debug.LogWarning($"[{GetType().Name}] Level complete event not assigned");
            
            LogDebug("Game audio events validation complete");
        }

        #endregion
    }
}
