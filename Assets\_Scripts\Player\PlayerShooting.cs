using System.Collections;
using System.Collections.Generic;
using System.Linq;
using MoreMountains.Feedbacks;
using ZLinq;
using PrimeTween;
using UnityEngine;
using UnityEngine.InputSystem;
using BTR.Projectiles;
using BTR.Player;

namespace BTR
{
    public class PlayerShooting : MonoBehaviour
    {
        private PlayerLocking playerLocking;
        private CrosshairCore crosshairCore;

        #region Shooting Variables
        [Header("Shooting Settings")]
        public float launchDelay = 0.1f;
        public MMF_Player shootFeedback;

        [SerializeField] private float projectileDamage = 10f;
        [SerializeField] private float projectileSpeed = 150f;
        [SerializeField] private float projectileScale = 1f;
        [SerializeField] private float nonTargetedSpeedMultiplier = 2f;

        [SerializeField] private LayerMask projectileLayer;
        [SerializeField] private LayerMask collisionIgnoreLayers;

        [SerializeField] private InputActionReference _shootAction;
        #endregion

        #region References
        private float lastFireTime;
        private WaitForSeconds launchDelayWait;
        #endregion

        private void Awake()
        {
            playerLocking = GetComponent<PlayerLocking>();
            crosshairCore = GetComponent<CrosshairCore>();

            if (playerLocking == null || crosshairCore == null)
            {
                Debug.LogError("Required components not found on the same GameObject.");
            }

            launchDelayWait = new WaitForSeconds(launchDelay);

            // Setup layer collision ignores
            projectileLayer = LayerMask.NameToLayer("Projectile");
            collisionIgnoreLayers = LayerMask.GetMask("Enemy", "Player", "Projectile");
        }

        private void OnDestroy()
        {
        }

        public void HandleShootingEffects()
        {
            // Trigger shooting audio through GameEvents
            if (GameEventsManager.Instance?.Events != null)
            {
                GameEventsManager.Instance.Events.TriggerPlayerShoot();
            }
            
            StartCoroutine(ShootVibrate());
            shootFeedback.PlayFeedbacks();
        }

        public IEnumerator LaunchProjectilesWithDelay()
        {
            Debug.Log($"[PlayerShooting] LaunchProjectilesWithDelay - STARTING");

            ProjectileManager.Instance.CompleteRunningJobs();
            crosshairCore.lastProjectileLaunchTime = Time.time;

            int lockedProjectileCount = playerLocking.GetLockedProjectileCount();
            IReadOnlyList<float> lockedDamages = playerLocking.GetLockedDamages();

            Debug.Log($"[PlayerShooting] LaunchProjectilesWithDelay - LockedCount: {lockedProjectileCount}, DamageCount: {lockedDamages.Count}");

            if (lockedProjectileCount <= 0)
            {
                Debug.Log($"[PlayerShooting] LaunchProjectilesWithDelay - No locked projectiles, exiting");
                yield break;
            }

            if (playerLocking.GetLockedEnemies().Count > 0)
            {
                IReadOnlyList<Transform> enemiesHit = playerLocking.GetLockedEnemies();
                Vector3 shootPosition = crosshairCore.RaySpawn.transform.position;

                int projectilesShot = 0;
                int damageIndex = 0;

                // Create a copy to avoid collection modification issues
                var enemiesCopy = new Transform[enemiesHit.Count];
                for (int i = 0; i < enemiesHit.Count; i++)
                {
                    enemiesCopy[i] = enemiesHit[i];
                }

                foreach (Transform enemy in enemiesCopy)
                {
                    if (projectilesShot >= lockedProjectileCount) break;
                    if (damageIndex >= lockedDamages.Count) break;
                    if (enemy == null) continue;

                    var enemyCore = enemy.GetComponent<EnemyCore>();
                    if (enemyCore == null || !enemyCore.IsAlive) continue;

                    Vector3 targetPosition = enemy.position;
                    float distance = Vector3.Distance(shootPosition, targetPosition);
                    float timeToTarget = distance / projectileSpeed;
                    float damageToApply = lockedDamages[damageIndex];

                    Vector3 direction = (targetPosition - shootPosition).normalized;
                    float projectileLifetime = timeToTarget + 0.5f;

                    // Apply damage immediately with a tiny delay for visual feedback
                    StartCoroutine(ApplyGuaranteedDamage(enemyCore, damageToApply, 0.05f));

                    IProjectile projectile = ProjectileManager.Instance.SpawnProjectileInterface(
                        shootPosition,
                        Quaternion.LookRotation(direction),
                        projectileSpeed,
                        projectileLifetime,
                        projectileScale,
                        0f,  // Set damage to 0 since this is just visual
                        false,
                        null,
                        enemy,
                        true
                    );

                    if (projectile != null)
                    {
                        Debug.Log($"[{GetType().Name}] Setting up targeted projectile. Speed: {projectileSpeed}, TimeToTarget: {timeToTarget}");
                        projectile.IsPlayerShot = true;

                        // Note: SetupForDirectHit and DisableCollisionDamage are legacy methods
                        // The new interface system handles this through the spawn parameters

                        Debug.Log($"[{GetType().Name}] Created visual projectile for target: {enemy.name}");
                    }

                    AnimateLockOnEffect();

                    projectilesShot++;
                    damageIndex++;
                    yield return launchDelayWait;
                }
            }
            else
            {
                for (int i = 0; i < lockedProjectileCount; i++)
                {
                    ShootNonTargetedProjectile();
                    yield return launchDelayWait;
                }
            }

            playerLocking.ResetLockCount();
        }

        private IEnumerator ApplyGuaranteedDamage(EnemyCore enemy, float damage, float visualDelay)
        {
            if (enemy == null) yield break;

            // Small delay for visual feedback
            yield return new WaitForSeconds(visualDelay);

            // Apply damage if enemy still exists and is alive
            if (enemy != null && enemy.IsAlive)
            {
                enemy.Damage(damage);
                Debug.Log($"[{GetType().Name}] Applied guaranteed damage: {damage} to {enemy.name}");
            }
        }

        // EnsureProjectileVisuals method removed - legacy method that used ProjectileStateBased-specific properties
        // The new interface system handles projectile movement and targeting automatically

        private void ShootNonTargetedProjectile()
        {
            if (ProjectileManager.Instance != null)
            {
                // Get the reticle's forward direction and position
                Vector3 shootDirection = crosshairCore.RaySpawn.transform.forward;
                Vector3 shootPosition = crosshairCore.RaySpawn.transform.position;

                float nonTargetedSpeed = projectileSpeed * nonTargetedSpeedMultiplier;

                Debug.Log($"[{GetType().Name}] Shooting non-targeted projectile - Direction: {shootDirection}, Speed: {nonTargetedSpeed}, Damage: {projectileDamage}");

                // Use the unified interface system
                IProjectile projectile = ProjectileManager.Instance.SpawnProjectileInterface(
                    shootPosition,
                    Quaternion.LookRotation(shootDirection),
                    nonTargetedSpeed,
                    10f, // lifetime
                    projectileScale,
                    projectileDamage,
                    false, // enableHoming
                    null, // material
                    null, // target
                    true // isPlayerProjectile
                );

                if (projectile != null)
                {
                    projectile.IsPlayerShot = true;
                    Debug.Log($"[{GetType().Name}] Successfully created non-targeted projectile");

                    // Handle any additional effects or feedback for shooting
                    HandleShootingEffects();
                }
                else
                {
                    Debug.LogError($"[{GetType().Name}] Failed to create non-targeted projectile!");
                }
            }
            else
            {
                Debug.LogError("[PlayerShooting] ProjectileSpawner.Instance is null!");
            }
        }

        public void AnimateLockOnEffect()
        {
            // The visual effect is now handled by UILockOnEffect
            // We only need to handle the shooting effects
            HandleShootingEffects();
        }

        private IEnumerator ShootVibrate()
        {
            // Implement vibration logic here
            yield return new WaitForSeconds(.1f);
        }
    }
}
