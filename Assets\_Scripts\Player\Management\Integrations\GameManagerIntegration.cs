using UnityEngine;
using BTR.Player;

namespace BTR.Player
{
    /// <summary>
    /// Integration between player system and GameManager.
    /// Handles game state synchronization and player lifecycle events.
    /// </summary>
    public class GameManagerIntegration : PlayerSystemIntegrationBase
    {
        private GameManager gameManager;
        private PlayerEntity currentPlayer;

        protected override void OnInitialize()
        {
            // Find GameManager
            gameManager = GameManager.Instance;

            if (gameManager == null)
            {
                Debug.LogWarning("[GameManagerIntegration] GameManager not found");
                return;
            }

            // Subscribe to GameManager events if available
            SubscribeToGameManagerEvents();

            Debug.Log("[GameManagerIntegration] Initialized");
        }

        protected override void OnCleanup()
        {
            UnsubscribeFromGameManagerEvents();
            currentPlayer = null;

            Debug.Log("[GameManagerIntegration] Cleaned up");
        }

        protected override void HandlePlayerSpawned(PlayerEntity player)
        {
            currentPlayer = player;

            if (gameManager != null)
            {
                // Notify GameManager of player spawn
                Debug.Log("[GameManagerIntegration] Player spawned");

                // GameManager will handle game state through its existing systems
            }

            Debug.Log("[GameManagerIntegration] Player spawned");
        }

        protected override void HandlePlayerDestroyed(PlayerEntity player)
        {
            if (gameManager != null)
            {
                // Notify GameManager of player destruction
                Debug.Log("[GameManagerIntegration] Player destroyed");
            }

            currentPlayer = null;

            Debug.Log("[GameManagerIntegration] Player destroyed");
        }

        protected override void HandlePlayerStateChanged(PlayerState oldState, PlayerState newState)
        {
            if (gameManager == null)
                return;

            // Handle specific state changes that affect game state
            switch (newState)
            {
                case PlayerState.Dead:
                    HandlePlayerDeath();
                    break;

                case PlayerState.Active:
                    if (oldState == PlayerState.Dead || oldState == PlayerState.Inactive)
                    {
                        HandlePlayerRevived();
                    }
                    break;

                case PlayerState.TimeControl:
                    HandleTimeControlActivated();
                    break;
            }

            // Notify GameManager of state change
            Debug.Log($"[GameManagerIntegration] Player state changed: {oldState} -> {newState}");
        }

        private void SubscribeToGameManagerEvents()
        {
            if (gameManager == null)
                return;

            // GameManager doesn't expose pause/resume events directly
            // We could subscribe to GameEvents instead, but for now just log
            Debug.Log("[GameManagerIntegration] GameManager events subscription - no direct events available");
        }

        private void UnsubscribeFromGameManagerEvents()
        {
            if (gameManager == null)
                return;

            // GameManager doesn't expose pause/resume events directly
            Debug.Log("[GameManagerIntegration] GameManager events unsubscription - no direct events to unsubscribe");
        }



        /// <summary>
        /// Handle game pause - can be called manually when game is paused
        /// </summary>
        public void OnGamePaused()
        {
            if (currentPlayer == null)
                return;

            // Pause player systems
            var timeControlComponent = currentPlayer.GetComponent<PlayerTimeControlComponent>();
            if (timeControlComponent != null)
            {
                timeControlComponent.ForceStopTimeEffects();
            }

            // Audio pause/resume methods removed from PlayerAudioComponent

            Debug.Log("[GameManagerIntegration] Player systems paused");
        }

        /// <summary>
        /// Handle game resume - can be called manually when game is resumed
        /// </summary>
        public void OnGameResumed()
        {
            if (currentPlayer == null)
                return;

            // Resume player systems
            // Audio enable/disable methods removed from PlayerAudioComponent

            Debug.Log("[GameManagerIntegration] Player systems resumed");
        }

        private void HandlePlayerDeath()
        {
            if (gameManager == null)
                return;

            // Check if this should trigger game over
            if (gameManager.IsGameOver())
            {
                HandleGameOver();
            }
            else
            {
                // Handle respawn logic - GameManager will handle this through its existing systems
                Debug.Log("[GameManagerIntegration] Player death handled by GameManager");
            }
        }

        private void HandlePlayerRevived()
        {
            if (gameManager == null)
                return;

            // Player revived - ensure game is not paused
            if (gameManager.IsGamePaused())
            {
                // Don't automatically unpause, let the game handle this
                Debug.Log("[GameManagerIntegration] Player revived while game is paused");
            }
        }

        private void HandleTimeControlActivated()
        {
            if (gameManager == null)
                return;

            // Notify GameManager that time control is active
            Debug.Log("[GameManagerIntegration] Time control activated");
        }

        private void HandleGameOver()
        {
            if (currentPlayer == null)
                return;

            // Disable all player systems on game over
            var inputComponent = currentPlayer.GetComponent<PlayerInputComponent>();
            if (inputComponent != null)
            {
                inputComponent.SetInputEnabled(false);
            }

            var movementComponent = currentPlayer.GetComponent<PlayerMovementComponent>();
            if (movementComponent != null)
            {
                movementComponent.SetMovementEnabled(false);
            }

            var shootingComponent = currentPlayer.GetComponent<PlayerShootingComponent>();
            if (shootingComponent != null)
            {
                shootingComponent.SetShootingEnabled(false);
            }
        }
    }
}
