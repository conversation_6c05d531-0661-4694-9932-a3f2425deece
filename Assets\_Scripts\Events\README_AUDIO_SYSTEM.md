# 🎵 BTR Audio System Architecture

## 📋 **QUICK SETUP GUIDE**

### **1. Create Audio Events ScriptableObject**
1. Right-click in Project window
2. Create > BTR > Events > AudioEvents
3. Name it "AudioEvents"
4. Place in `Assets/_Scripts/Events/` folder

### **2. Setup AudioEventsManager**
1. Create empty GameObject named "AudioEventsManager"
2. Add `AudioEventsManager` component
3. Assign the AudioEvents ScriptableObject to the `audioEvents` field
4. Mark as DontDestroyOnLoad (handled automatically)

### **3. Verify PlayerAudioComponent**
1. Ensure PlayerAudioComponent is on Player GameObject
2. Assign all FMOD EventReference fields
3. Enable `enableAudio = true`
4. Enable `debugAudio = true` for testing

### **4. Test the System**
- Use context menu "Test Shoot Sound" on PlayerAudioComponent
- Check console for debug logs
- Verify audio plays when player shoots, dodges, locks targets

---

## 🏗️ **SYSTEM ARCHITECTURE**

### **Event-Driven Design**
```
Player Action → AudioEventsManager.TriggerXXX() → AudioEvents.OnXXX → PlayerAudioComponent.PlayXXX()
```

### **Key Components:**

#### **1. AudioEvents.cs** - ScriptableObject
- **Purpose**: Centralized event definitions for all audio triggers
- **Events**: Player combat, health, time control, UI, enemy, game events
- **Benefits**: Decoupled, serializable, easy to manage

#### **2. AudioEventsManager.cs** - Singleton Manager
- **Purpose**: Manages AudioEvents instance and auto-subscribes PlayerAudioComponent
- **Initialization**: DontDestroyOnLoad, waits for PlayerAudioComponent
- **API**: Static methods for quick audio triggering

#### **3. PlayerAudioComponent.cs** - Audio Player
- **Purpose**: FMOD event playback, pooling, 3D audio positioning
- **Integration**: Subscribes to AudioEvents automatically
- **Features**: Event pooling, debug support, singleton access

---

## 🔄 **INTEGRATION FLOW**

### **Player Shooting Example:**
```csharp
// OLD WAY (Direct Coupling):
PlayerAudioComponent.Instance.PlayShootingEffects();

// NEW WAY (Event-Driven):
AudioEventsManager.TriggerPlayerShoot();
```

### **Event Flow:**
1. **PlayerShooting.HandleShootingEffects()** calls `AudioEventsManager.TriggerPlayerShoot()`
2. **AudioEventsManager** triggers `audioEvents.TriggerPlayerShoot()`
3. **AudioEvents** fires `OnPlayerShoot` event
4. **PlayerAudioComponent** receives event and calls `PlayShootingSound()`
5. **FMOD** plays the audio with pooling and 3D positioning

---

## 🎯 **BENEFITS OF NEW ARCHITECTURE**

### **1. Decoupling**
- ✅ Player components don't directly reference PlayerAudioComponent
- ✅ Easy to swap audio implementations
- ✅ Testable without audio dependencies

### **2. Consistency**
- ✅ All audio triggers use the same event pattern
- ✅ No more mixed direct calls and event subscriptions
- ✅ Centralized audio event management

### **3. Maintainability**
- ✅ Single location for all audio event definitions
- ✅ Easy to add new audio events
- ✅ Clear separation of concerns

### **4. Performance**
- ✅ Event pooling for high-frequency sounds
- ✅ Automatic cleanup and resource management
- ✅ 3D audio positioning handled centrally

---

## 📝 **COMPONENT UPDATES**

### **Updated Components:**
- ✅ **PlayerShooting.cs** - Uses `AudioEventsManager.TriggerPlayerShoot()`
- ✅ **PlayerDodgeComponent.cs** - Uses `AudioEventsManager.TriggerPlayerDodge()`
- ✅ **PlayerLockingComponent.cs** - Uses `AudioEventsManager.TriggerPlayerLockTarget()`

### **Health Integration:**
- ✅ **PlayerHealthComponent** fires damage/death events
- ✅ **PlayerAudioComponent** subscribes to health events automatically
- ✅ Damage and death audio plays without direct calls

---

## 🔧 **DEBUGGING & TESTING**

### **Debug Features:**
- **PlayerAudioComponent**: Enable `debugAudio` for detailed logs
- **Context Menu**: "Test Shoot Sound", "Test Damage Sound", etc.
- **AudioEventsManager**: Logs subscription status and initialization

### **Testing Checklist:**
- [ ] Player shooting audio plays
- [ ] Player dodge audio plays  
- [ ] Player lock target audio plays
- [ ] Player damage audio plays (when taking damage)
- [ ] Player death audio plays
- [ ] No console errors about missing components
- [ ] Audio events are properly pooled (check FMOD profiler)

---

## 🚀 **FUTURE ENHANCEMENTS**

### **Planned Features:**
- **Enemy Audio Events**: Centralized enemy sound management
- **UI Audio Events**: Menu sounds, button clicks, notifications
- **Music Integration**: Dynamic music parameter changes via events
- **Audio Zones**: Location-based audio event filtering
- **Audio Analytics**: Track most-used audio events for optimization

### **Integration Points:**
- **GameEvents**: Potential integration with existing GameEvents system
- **AudioManager**: Music parameter changes via audio events
- **UI Systems**: Menu and interface audio via events
- **Enemy Systems**: Enemy audio centralization

---

## ⚠️ **IMPORTANT NOTES**

### **Setup Requirements:**
1. **AudioEvents ScriptableObject** must be created and assigned
2. **AudioEventsManager** must exist in scene before PlayerAudioComponent
3. **PlayerAudioComponent** must have all FMOD events assigned
4. **FMOD** events must exist in FMOD project

### **Migration Notes:**
- Old direct `PlayerAudioComponent.Instance` calls still work as fallback
- Gradual migration possible - components can use both approaches
- Event system provides better architecture for new features

### **Performance Considerations:**
- Events have minimal overhead compared to direct calls
- FMOD event pooling prevents memory leaks
- 3D audio positioning handled efficiently
- Debug logging should be disabled in production builds

---

## 📞 **QUICK REFERENCE**

### **Common Audio Triggers:**
```csharp
// Player Actions
AudioEventsManager.TriggerPlayerShoot();
AudioEventsManager.TriggerPlayerDodge();
AudioEventsManager.TriggerPlayerLockTarget();
AudioEventsManager.TriggerPlayerDamaged(damage);

// Direct Event Access
var audioEvents = AudioEventsManager.GetAudioEvents();
audioEvents?.TriggerPlayerDeath();
audioEvents?.TriggerWaveComplete();
```

### **Adding New Audio Events:**
1. Add event to `AudioEvents.cs`
2. Add trigger method to `AudioEvents.cs`
3. Add subscription in `AudioEventsManager.cs`
4. Add playback method to `PlayerAudioComponent.cs`
5. Call trigger method from gameplay code

This architecture provides a solid foundation for scalable, maintainable audio management in BTR.
