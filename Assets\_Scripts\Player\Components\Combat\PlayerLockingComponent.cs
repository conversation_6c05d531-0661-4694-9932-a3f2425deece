using System.Collections.Generic;
using System.Linq;
using UnityEngine;
using FMODUnity;
using MoreMountains.Feedbacks;
using BTR.Player;
using BTR.Projectiles;
using ZLinq;

namespace BTR.Player
{
    /// <summary>
    /// Handles player target locking for both projectiles and enemies.
    /// Refactored from PlayerLocking.cs to use component-based architecture.
    /// </summary>
    public class PlayerLockingComponent : PlayerComponent, IInputPlayerComponent, IStatefulPlayerComponent
    {
        [Header("Locking Settings")]
        [SerializeField] private int maxLockedTargets = 6;
        [SerializeField] private int maxLockedEnemies = 3;
        [SerializeField] private float lockRange = 30f;
        [SerializeField] private float lockAngle = 45f;
        [SerializeField] private float bulletLockInterval = 0.05f;
        [SerializeField] private float enemyLockInterval = 0.2f;

        [Header("Detection")]
        [SerializeField] private Vector3 detectionBoxSize = new Vector3(4, 4, 400);
        [SerializeField] private float detectionBoxWidthMultiplier = 1.5f;
        [SerializeField] private float offsetFromReticle = 2f;
        [SerializeField] private LayerMask projectileLayer = 1 << 13;
        [SerializeField] private LayerMask enemyLayer = 1 << 6;

        [Header("Effects")]
        [SerializeField] private MMF_Player lockFeedback;
        [SerializeField] private GameObject lockIndicator;
        [SerializeField] private GameObject lockHighlightPrefab;

        // Helper struct for CheckEnemyLock processing
        private readonly struct EnemyScanInfo
        {
            public readonly Collider SourceCollider;
            public readonly Transform EnemyTransform;
            public readonly float SqrDistance;
            public readonly float Angle;
            public readonly bool IsLockable;
            public readonly bool IsAlreadyLockedByPlayer;

            public EnemyScanInfo(Collider collider, Vector3 reticlePos, Vector3 reticleForward, List<Transform> currentLockedEnemiesList)
            {
                SourceCollider = collider;
                if (collider == null) // Basic safety check
                {
                    EnemyTransform = null;
                    IsLockable = false;
                    SqrDistance = float.MaxValue;
                    Angle = float.MaxValue;
                    IsAlreadyLockedByPlayer = false;
                    return;
                }
                EnemyTransform = collider.transform;
                IsLockable = collider.GetComponent<NonLockableEnemy>() == null;

                if (!IsLockable) // Avoid further calculations if not lockable
                {
                    SqrDistance = float.MaxValue;
                    Angle = float.MaxValue;
                    IsAlreadyLockedByPlayer = false;
                    return;
                }

                Vector3 directionToEnemy = EnemyTransform.position - reticlePos;
                SqrDistance = directionToEnemy.sqrMagnitude; // Use sqrMagnitude for performance
                Angle = Vector3.Angle(reticleForward, directionToEnemy);
                IsAlreadyLockedByPlayer = currentLockedEnemiesList.Contains(EnemyTransform);
            }
        }

        // Component references
        private CrosshairCore crosshairCore;
        private SquareReticle squareReticle;
        private AimAssistController aimAssistController;
        private CombatConfiguration combatConfig;

        // Locking state
        private List<Transform> lockedEnemies = new List<Transform>();
        private Collider[] _nearbyCollidersBuffer;
        private const int MaxNearbyColliders = 32; // Max potential enemies to check in one overlap sphere
        private RaycastHit[] _sphereCastHitsBuffer;
        private const int MaxSphereCastHits = 16;
        private Collider[] _boxOverlapCollidersBuffer;
        private const int MaxBoxOverlapColliders = 16;
        private List<float> lockedProjectileDamages = new List<float>();
        private Dictionary<Transform, float> enemyLockTimes = new Dictionary<Transform, float>();
        private Dictionary<Transform, GameObject> lockHighlights = new Dictionary<Transform, GameObject>();
        private Transform currentLockedEnemy;
        private int lockedProjectileCount;
        private int totalLocks;

        // Timing
        private float lastBulletLockTime;
        private float lastEnemyLockTime;
        private float lockTimer;

        // Input handling
        public bool ShouldReceiveInput => IsEnabled;
        private bool lockPressed;

        #region PlayerComponent Overrides

        protected override void OnComponentInitialize()
        {
            InitializeReferences();
            LoadConfiguration();
            _nearbyCollidersBuffer = new Collider[MaxNearbyColliders];
            _sphereCastHitsBuffer = new RaycastHit[MaxSphereCastHits];
            _boxOverlapCollidersBuffer = new Collider[MaxBoxOverlapColliders];
        }

        protected override void OnComponentStarted()
        {
            ValidateReferences();
            InitializeLockingState();
        }

        public override void OnComponentUpdate()
        {
            HandleLockingInput();
            UpdateLocking();
        }

        protected override bool OnComponentValidate()
        {
            if (crosshairCore == null)
            {
                LogError("CrosshairCore component is required");
                return false;
            }

            return true;
        }

        #endregion

        #region Initialization

        private void InitializeReferences()
        {
            crosshairCore = GetComponent<CrosshairCore>();
            squareReticle = FindFirstObjectByType<SquareReticle>();
            aimAssistController = GetComponent<AimAssistController>();
        }

        private void LoadConfiguration()
        {
            var playerCore = GetComponent<PlayerCore>();
            if (playerCore != null && playerCore.Configuration != null)
            {
                combatConfig = playerCore.Configuration.CombatConfig;
                ApplyConfiguration();
            }
        }

        private void ApplyConfiguration()
        {
            if (combatConfig != null)
            {
                lockRange = combatConfig.LockRange;
                lockAngle = combatConfig.LockAngle;
                maxLockedTargets = combatConfig.MaxTargets;
                maxLockedEnemies = combatConfig.MaxLockedTargets;
                bulletLockInterval = combatConfig.BulletLockInterval;
                enemyLockInterval = combatConfig.EnemyLockInterval;
                detectionBoxSize = combatConfig.DetectionBoxSize;
                detectionBoxWidthMultiplier = combatConfig.DetectionBoxWidthMultiplier;
                offsetFromReticle = combatConfig.OffsetFromReticle;
                projectileLayer = combatConfig.ProjectileLayers;
                enemyLayer = combatConfig.TargetLayers;
            }
        }

        private void ValidateReferences()
        {
            if (crosshairCore == null)
            {
                LogError("CrosshairCore not found!");
            }

            if (squareReticle == null)
            {
                LogWarning("SquareReticle not found in scene");
            }
        }

        private void InitializeLockingState()
        {
            lockedEnemies.Clear();
            lockedProjectileDamages.Clear();
            enemyLockTimes.Clear();
            lockHighlights.Clear();

            currentLockedEnemy = null;
            lockedProjectileCount = 0;
            totalLocks = 0;

            lastBulletLockTime = 0f;
            lastEnemyLockTime = 0f;
            lockTimer = 0f;
        }

        #endregion

        #region Input Handling

        public void HandleInput(PlayerInputData inputData)
        {
            if (!ShouldReceiveInput)
                return;

            lockPressed = inputData.lockTarget;
        }

        private void HandleLockingInput()
        {
            if (lockPressed && crosshairCore != null && crosshairCore.CheckLockProjectiles())
            {
                TryLockProjectiles();
            }
        }

        #endregion

        #region Locking Logic

        private void UpdateLocking()
        {
            if (lockedProjectileCount > 0)
            {
                CheckEnemyLock();
            }
            else
            {
                ClearEnemyLocks();
            }
        }

        private void TryLockProjectiles()
        {
            if (!IsTimeToLockBullet() || lockedProjectileCount >= maxLockedTargets)
                return;

            Vector3 reticlePos = crosshairCore.RaySpawn.transform.position;
            Vector3 reticleForward = crosshairCore.RaySpawn.transform.forward;

            // Create detection box
            Vector3 adjustedBoxSize = detectionBoxSize;
            adjustedBoxSize.x *= detectionBoxWidthMultiplier;
            adjustedBoxSize.y *= detectionBoxWidthMultiplier;

            Vector3 boxCenter = reticlePos + (reticleForward * (adjustedBoxSize.z / 2f + offsetFromReticle));
            Quaternion boxRotation = crosshairCore.RaySpawn.transform.rotation;

            // Sphere cast for fast projectiles
            float sphereRadius = Mathf.Max(adjustedBoxSize.x, adjustedBoxSize.y) / 2f;
            int numSphereHits = Physics.SphereCastNonAlloc(reticlePos, sphereRadius, reticleForward, _sphereCastHitsBuffer, lockRange, projectileLayer);

            for (int i = 0; i < numSphereHits; i++)
            {
                if (TryLockProjectile(_sphereCastHitsBuffer[i]))
                {
                    lastBulletLockTime = Time.time;
                    break; // Found a projectile to lock via sphere cast
                }
            }

            // Box overlap as backup
            if (lockedProjectileCount < maxLockedTargets) // Check again, as sphere cast might have locked one
            {
                int numBoxHits = Physics.OverlapBoxNonAlloc(boxCenter, adjustedBoxSize / 2f, _boxOverlapCollidersBuffer, boxRotation, projectileLayer);

                for (int i = 0; i < numBoxHits; i++)
                {
                    Collider hitCollider = _boxOverlapCollidersBuffer[i];
                    Vector3 dirToProjectile = (hitCollider.transform.position - reticlePos).normalized;
                    // A single raycast here is fine, the main allocation was from OverlapBox producing an array.
                    if (Physics.Raycast(reticlePos, dirToProjectile, out RaycastHit rayHit, lockRange, projectileLayer))
                    {
                        if (TryLockProjectile(rayHit))
                        {
                            lastBulletLockTime = Time.time;
                            break; // Found a projectile to lock via box overlap + raycast
                        }
                    }
                }
            }
        }

        private bool TryLockProjectile(RaycastHit hit)
        {
            var projectile = hit.transform.GetComponent<IProjectile>();
            if (projectile == null || projectile.IsPlayerShot)
                return false;

            if (lockedProjectileCount >= maxLockedTargets)
                return false;

            // Store projectile damage
            float damageToStore = projectile.DamageAmount;
            lockedProjectileDamages.Add(damageToStore);

            // Lock the projectile
            projectile.IsPlayerShot = true;

            // Play effects
            PlayLockEffects();

            // Update counters
            lockedProjectileCount++;
            totalLocks++;

            // Visual feedback
            if (squareReticle != null)
            {
                squareReticle.LockOnTarget(hit.transform);
            }

            // Enable projectile visual indicator
            var visualChild = hit.transform.GetChild(0);
            if (visualChild != null)
            {
                visualChild.gameObject.SetActive(true);
            }

            LogDebug($"Locked projectile - Count: {lockedProjectileCount}, Damage: {damageToStore}");
            return true;
        }

        private void CheckEnemyLock()
        {
            if (Time.time < lastEnemyLockTime + enemyLockInterval)
                return;

            lastEnemyLockTime = Time.time;

            Vector3 reticlePos = crosshairCore.RaySpawn.transform.position;
            Vector3 reticleForward = crosshairCore.RaySpawn.transform.forward;

            EnemyScanInfo nearestEnemyCandidate = default;
            float nearestSqrDistance = float.MaxValue;
            bool foundCandidateThisFrame = false;

            // Find enemies in range and process them
            int numHits = Physics.OverlapSphereNonAlloc(reticlePos, lockRange, _nearbyCollidersBuffer, enemyLayer);

            var potentialTargets = new System.ArraySegment<Collider>(_nearbyCollidersBuffer, 0, numHits)
                .AsValueEnumerable() // Process only the valid hits from the buffer via ArraySegment
                .Select(c => new EnemyScanInfo(c, reticlePos, reticleForward, lockedEnemies))
                .Where(info => info.IsLockable);

            foreach (var info in potentialTargets)
            {
                float angleThreshold = info.IsAlreadyLockedByPlayer ? lockAngle * 1.5f : lockAngle;

                if (info.Angle <= angleThreshold)
                {
                    if (info.SqrDistance < nearestSqrDistance)
                    {
                        nearestSqrDistance = info.SqrDistance;
                        nearestEnemyCandidate = info;
                        foundCandidateThisFrame = true;
                    }
                }
                else if (info.IsAlreadyLockedByPlayer)
                {
                    // Enemy is already locked but now outside the wider cone, manage grace period
                    if (!enemyLockTimes.ContainsKey(info.EnemyTransform))
                    {
                        enemyLockTimes[info.EnemyTransform] = Time.time;
                    }
                    else if (Time.time - enemyLockTimes[info.EnemyTransform] > 1f) // Grace period expired
                    {
                        UnlockEnemy(info.EnemyTransform);
                    }
                }
            }

            // Lock onto the best candidate found this frame
            if (foundCandidateThisFrame && nearestEnemyCandidate.EnemyTransform != null)
            {
                if (!nearestEnemyCandidate.IsAlreadyLockedByPlayer) // Only call LockEnemy if it's a new lock
                {
                    LockEnemy(nearestEnemyCandidate.EnemyTransform);
                }
                currentLockedEnemy = nearestEnemyCandidate.EnemyTransform;
                enemyLockTimes.Remove(nearestEnemyCandidate.EnemyTransform); // Remove from grace period tracking if re-locked

                // Update aim assist
                if (aimAssistController != null)
                {
                    aimAssistController.SetCurrentLockedEnemy(currentLockedEnemy);
                    aimAssistController.ApplyAimAssist(crosshairCore.RaySpawn.transform, Camera.main.transform);
                }
            }
            else if (lockedEnemies.Count > 0) // No valid candidate found, check if existing locks should be cleared
            {
                // Check if any locked enemies (still in grace) should be unlocked
                // This re-iterates enemyLockTimes, which is small. Could optimize if it becomes a bottleneck.
                bool shouldClearAll = !enemyLockTimes.AsValueEnumerable().Any(kvp => Time.time - kvp.Value <= 1f);
                if (shouldClearAll)
                {
                    ClearEnemyLocks();
                    currentLockedEnemy = null; // Ensure currentLockedEnemy is also cleared
                }
            }
            // If no candidate was found and lockedEnemies.Count was 0, currentLockedEnemy remains null correctly.
        }

        private void LockEnemy(Transform enemy)
        {
            if (lockedEnemies.Contains(enemy) || lockedEnemies.Count >= maxLockedEnemies)
                return;

            lockedEnemies.Add(enemy);

            // Play audio - trigger GameEvents
            if (GameEventsManager.Instance?.Events != null)
            {
                GameEventsManager.Instance.Events.TriggerPlayerLockTarget();
            }
            else
            {
                // Fallback to direct FMOD call
                RuntimeManager.PlayOneShot("event:/Player/LockEnemy");
            }

            // Visual feedback
            if (squareReticle != null)
            {
                squareReticle.LockOnEnemy(enemy);
            }

            // Create lock highlight
            if (lockHighlightPrefab != null && !lockHighlights.ContainsKey(enemy))
            {
                GameObject highlight = Instantiate(lockHighlightPrefab, enemy);
                lockHighlights[enemy] = highlight;
            }

            // Show lock indicator
            if (lockIndicator != null)
            {
                lockIndicator.SetActive(true);
            }

            // Play feedback
            if (lockFeedback != null)
            {
                lockFeedback.PlayFeedbacks();
            }

            LogDebug($"Locked enemy: {enemy.name}");
        }

        private void UnlockEnemy(Transform enemy)
        {
            if (!lockedEnemies.Contains(enemy))
                return;

            lockedEnemies.Remove(enemy);
            enemyLockTimes.Remove(enemy);

            // Remove highlight
            if (lockHighlights.ContainsKey(enemy))
            {
                if (lockHighlights[enemy] != null)
                {
                    Destroy(lockHighlights[enemy]);
                }
                lockHighlights.Remove(enemy);
            }

            // Hide indicator if no enemies locked
            if (lockedEnemies.Count == 0 && lockIndicator != null)
            {
                lockIndicator.SetActive(false);
            }

            LogDebug($"Unlocked enemy: {enemy.name}");
        }

        private void ClearEnemyLocks()
        {
            foreach (var enemy in lockedEnemies.AsValueEnumerable())
            {
                UnlockEnemy(enemy);
            }

            currentLockedEnemy = null;

            if (lockIndicator != null)
            {
                lockIndicator.SetActive(false);
            }
        }

        #endregion

        #region Effects

        private void PlayLockEffects()
        {
            // Audio
            if (PlayerAudioReferences.Instance != null)
            {
                PlayerAudioReferences.Instance.PlayLockTarget();
            }
            else
            {
                // Fallback to direct FMOD call
                RuntimeManager.PlayOneShot("event:/Player/LockProjectile");
            }

            // Feedback
            if (lockFeedback != null)
            {
                lockFeedback.PlayFeedbacks();
            }
        }

        #endregion

        #region Timing

        private bool IsTimeToLockBullet()
        {
            return Time.time >= lastBulletLockTime + bulletLockInterval;
        }

        #endregion

        #region IStatefulPlayerComponent Implementation

        public void OnPlayerStateChanged(PlayerState oldState, PlayerState newState)
        {
            switch (newState)
            {
                case PlayerState.Dead:
                case PlayerState.Inactive:
                    ClearAllLocks();
                    break;
            }
        }

        public bool CanTransitionToState(PlayerState targetState)
        {
            // Locking component doesn't restrict state transitions
            return true;
        }

        #endregion

        #region Public API

        /// <summary>
        /// Get the number of locked projectiles
        /// </summary>
        /// <returns>Locked projectile count</returns>
        public int GetLockedProjectileCount()
        {
            return lockedProjectileCount;
        }

        /// <summary>
        /// Gets a value indicating whether there are any currently locked enemies.
        /// </summary>
        public bool HasLockedEnemies => lockedEnemies.Count > 0;

        /// <summary>
        /// Get the list of locked projectile damages
        /// </summary>
        /// <returns>Read-only list of damages</returns>
        public IReadOnlyList<float> GetLockedDamages()
        {
            return lockedProjectileDamages.AsReadOnly();
        }

        /// <summary>
        /// Get the list of locked enemies
        /// </summary>
        /// <returns>List of enemy transforms</returns>
        public IReadOnlyList<Transform> GetLockedEnemies()
        {
            return lockedEnemies.AsReadOnly();
        }

        /// <summary>
        /// Get locked targets for shooting strategies
        /// </summary>
        /// <returns>Array of locked targets</returns>
        public Transform[] GetLockedTargets()
        {
            return lockedEnemies.AsValueEnumerable().ToArray();
        }

        /// <summary>
        /// Reset all lock counts (called after shooting)
        /// </summary>
        public void ResetLockCount()
        {
            lockedProjectileCount = 0;
            totalLocks = 0;
            lockedProjectileDamages.Clear();
            ClearEnemyLocks();
        }

        /// <summary>
        /// Clear all locks immediately
        /// </summary>
        public void ClearAllLocks()
        {
            ResetLockCount();
            ClearEnemyLocks();
        }

        /// <summary>
        /// Get the current locked enemy
        /// </summary>
        /// <returns>Currently locked enemy transform</returns>
        public Transform GetCurrentLockedEnemy()
        {
            return currentLockedEnemy;
        }

        /// <summary>
        /// Get total number of locks
        /// </summary>
        /// <returns>Total locks</returns>
        public int GetTotalLocks()
        {
            return totalLocks;
        }

        #endregion
    }
}
