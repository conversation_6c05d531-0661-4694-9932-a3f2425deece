using UnityEngine;
using System;
using UnityEngine.SceneManagement;

namespace BTR
{
    [CreateAssetMenu(fileName = "GameEvents", menuName = "BTR/Events/GameEvents")]
    public class GameEvents : ScriptableObject
    {
        // Player Events
        public event Action OnPlayerDeath;
        public event Action<float, float> OnPlayerHealthChanged;
        public event Action<int> OnPlayerScoreChanged;
        public event Action<bool> OnPlayerInvincibilityChanged;
        public event Action<float> OnPlayerDamaged;
        public event Action<float> OnPlayerStaminaChanged;
        
        // Player Action Events
        public event Action OnPlayerShoot;
        public event Action OnPlayerDodge;
        public event Action OnPlayerLockTarget;
        public event Action OnPlayerReload;

        // Game State Events
        public event Action OnGameStarted;
        public event Action OnGameOver;
        public event Action OnGamePause;
        public event Action OnGameResume;
        public event Action OnGameRestart;
        public event Action OnGameRestarted;
        public event Action OnPlayerDied;
        public event Action OnTimePaused;
        public event Action OnTimeResumed;

        // Wave Events
        public event Action<int> OnWaveStarted;
        public event Action OnWaveCompleted;
        public event Action<int> OnWaveCountUpdated;

        // Enemy Events
        public event Action<Transform> OnEnemySpawned;
        public event Action<Transform> OnEnemyDeath;
        public event Action<Transform, float> OnEnemyDamaged;
        public event Action<Transform> OnEnemyStartedMoving;
        public event Action<Transform> OnEnemyStoppedMoving;
        public event Action<Transform> OnEnemyProjectileFired;
        public event Action<Transform> OnEnemyExplosionStarted;
        public event Action<Transform> OnEnemyExplosion;
        public event Action<EnemyCore> OnEnemyStateChanged;

        // Kill Streak Events
        public event Action<int, float> OnEnemyKilledWithStreak; // streak count, multiplier
        public event Action<int, float> OnStreakChanged; // streak count, multiplier
        public event Action<int, string> OnStreakMilestone; // streak count, milestone name
        public event Action OnStreakEnded;

        // Scene Events
        public event Action<string> OnSceneLoadStarted;
        public event Action<float> OnSceneLoadProgress;
        public event Action OnSceneLoadCompleted;
        public event Action<string> OnSceneTransition;
        public event Action<Scene, LoadSceneMode> OnSceneLoaded;
        public event Action<Scene, LoadSceneMode> OnSceneUnloaded;
        public event Action<Scene, LoadSceneMode> OnSceneInitializationRequested;
        public event Action OnSceneTransitionStarted;
        public event Action OnSceneTransitionCompleted;

        // Score Events
        public event Action OnScoreReset;
        public event Action<int> OnScoreUpdated;
        public event Action<int> OnHighScoreUpdated;

        // Camera Events
        public event Action OnTransitionCameraRequested;
        public event Action OnStartingTransition;
        
        // VFX Events
        public event Action OnWaveEndedVFX;

        private void OnDisable() => ClearAllSubscriptions();

        #region Trigger Methods
        // Player Triggers
        public void TriggerPlayerDeath() => OnPlayerDeath?.Invoke();
        public void TriggerPlayerHealthChanged(float currentHealth, float maxHealth) => OnPlayerHealthChanged?.Invoke(currentHealth, maxHealth);
        public void TriggerPlayerScoreChanged(int score) => OnPlayerScoreChanged?.Invoke(score);
        public void TriggerPlayerInvincibilityChanged(bool isInvincible) => OnPlayerInvincibilityChanged?.Invoke(isInvincible);
        public void TriggerPlayerDamaged(float damage) => OnPlayerDamaged?.Invoke(damage);
        public void TriggerPlayerStaminaChanged(float stamina) => OnPlayerStaminaChanged?.Invoke(stamina);
        
        // Player Action Triggers
        public void TriggerPlayerShoot() => OnPlayerShoot?.Invoke();
        public void TriggerPlayerDodge() => OnPlayerDodge?.Invoke();
        public void TriggerPlayerLockTarget() => OnPlayerLockTarget?.Invoke();
        public void TriggerPlayerReload() => OnPlayerReload?.Invoke();

        // Game State Triggers
        public void TriggerGameStarted() => OnGameStarted?.Invoke();
        public void TriggerGameOver() => OnGameOver?.Invoke();
        public void TriggerGamePause() => OnGamePause?.Invoke();
        public void TriggerGameResume() => OnGameResume?.Invoke();
        public void TriggerGameRestart() => OnGameRestart?.Invoke();
        public void TriggerGameRestarted() => OnGameRestarted?.Invoke();
        public void TriggerPlayerDied() => OnPlayerDied?.Invoke();
        public void TriggerTimePaused() => OnTimePaused?.Invoke();
        public void TriggerTimeResumed() => OnTimeResumed?.Invoke();

        // Wave Triggers
        public void TriggerWaveStarted(int wave) => OnWaveStarted?.Invoke(wave);
        public void TriggerWaveCompleted() => OnWaveCompleted?.Invoke();
        public void TriggerWaveCountUpdated(int count) => OnWaveCountUpdated?.Invoke(count);

        // Enemy Triggers
        public void TriggerEnemySpawned(Transform enemy) => OnEnemySpawned?.Invoke(enemy);
        public void TriggerEnemyDeath(Transform enemy) => OnEnemyDeath?.Invoke(enemy);
        public void TriggerEnemyDamaged(Transform enemy, float damage) => OnEnemyDamaged?.Invoke(enemy, damage);
        public void TriggerEnemyStartedMoving(Transform enemy) => OnEnemyStartedMoving?.Invoke(enemy);
        public void TriggerEnemyStoppedMoving(Transform enemy) => OnEnemyStoppedMoving?.Invoke(enemy);
        public void TriggerEnemyProjectileFired(Transform enemy) => OnEnemyProjectileFired?.Invoke(enemy);
        public void TriggerEnemyExplosionStarted(Transform enemy) => OnEnemyExplosionStarted?.Invoke(enemy);
        public void TriggerEnemyExplosion(Transform enemy) => OnEnemyExplosion?.Invoke(enemy);
        public void TriggerEnemyStateChanged(EnemyCore enemy) => OnEnemyStateChanged?.Invoke(enemy);

        // Kill Streak Triggers
        public void TriggerEnemyKilledWithStreak(int streakCount, float multiplier) => OnEnemyKilledWithStreak?.Invoke(streakCount, multiplier);
        public void TriggerStreakChanged(int streakCount, float multiplier) => OnStreakChanged?.Invoke(streakCount, multiplier);
        public void TriggerStreakMilestone(int streakCount, string milestoneName) => OnStreakMilestone?.Invoke(streakCount, milestoneName);
        public void TriggerStreakEnded() => OnStreakEnded?.Invoke();

        // Scene Triggers
        public void TriggerSceneLoadStarted(string sceneName) => OnSceneLoadStarted?.Invoke(sceneName);
        public void TriggerSceneLoadProgress(float progress) => OnSceneLoadProgress?.Invoke(progress);
        public void TriggerSceneLoadCompleted() => OnSceneLoadCompleted?.Invoke();
        public void TriggerSceneTransition(string sceneName) => OnSceneTransition?.Invoke(sceneName);
        public void TriggerSceneLoaded(Scene scene, LoadSceneMode mode) => OnSceneLoaded?.Invoke(scene, mode);
        public void TriggerSceneUnloaded(Scene scene, LoadSceneMode mode) => OnSceneUnloaded?.Invoke(scene, mode);
        public void TriggerSceneInitializationRequested(Scene scene, LoadSceneMode mode) => OnSceneInitializationRequested?.Invoke(scene, mode);
        public void TriggerSceneTransitionStarted() => OnSceneTransitionStarted?.Invoke();
        public void TriggerSceneTransitionCompleted() => OnSceneTransitionCompleted?.Invoke();

        // Score Triggers
        public void TriggerScoreReset() => OnScoreReset?.Invoke();
        public void TriggerScoreUpdated(int score) => OnScoreUpdated?.Invoke(score);
        public void TriggerHighScoreUpdated(int highScore) => OnHighScoreUpdated?.Invoke(highScore);

        // Camera Event Triggers
        public void TriggerTransitionCameraRequested() => OnTransitionCameraRequested?.Invoke();
        public void TriggerStartingTransition() => OnStartingTransition?.Invoke();
        
        // VFX Event Triggers
        public void TriggerWaveEndedVFX() => OnWaveEndedVFX?.Invoke();
        #endregion

        public void ClearAllSubscriptions()
        {
            OnPlayerDeath = null;
            OnPlayerHealthChanged = null;
            OnPlayerScoreChanged = null;
            OnPlayerInvincibilityChanged = null;
            OnPlayerDamaged = null;
            OnPlayerStaminaChanged = null;
            
            // Player Action Events
            OnPlayerShoot = null;
            OnPlayerDodge = null;
            OnPlayerLockTarget = null;
            OnPlayerReload = null;

            OnGameStarted = null;
            OnGameOver = null;
            OnGamePause = null;
            OnGameResume = null;
            OnGameRestart = null;
            OnGameRestarted = null;
            OnPlayerDied = null;
            OnTimePaused = null;
            OnTimeResumed = null;

            OnWaveStarted = null;
            OnWaveCompleted = null;
            OnWaveCountUpdated = null;

            OnEnemySpawned = null;
            OnEnemyDeath = null;
            OnEnemyDamaged = null;
            OnEnemyStartedMoving = null;
            OnEnemyStoppedMoving = null;
            OnEnemyProjectileFired = null;
            OnEnemyExplosionStarted = null;
            OnEnemyExplosion = null;
            OnEnemyStateChanged = null;

            OnEnemyKilledWithStreak = null;
            OnStreakChanged = null;
            OnStreakMilestone = null;
            OnStreakEnded = null;

            OnSceneLoadStarted = null;
            OnSceneLoadProgress = null;
            OnSceneLoadCompleted = null;
            OnSceneTransition = null;
            OnSceneLoaded = null;
            OnSceneUnloaded = null;
            OnSceneInitializationRequested = null;
            OnSceneTransitionStarted = null;
            OnSceneTransitionCompleted = null;

            OnScoreReset = null;
            OnScoreUpdated = null;
            OnHighScoreUpdated = null;

            // Camera Events
            OnTransitionCameraRequested = null;
            OnStartingTransition = null;

            // VFX Events
            OnWaveEndedVFX = null;
        }
    }
}