using UnityEngine;
using BTR;
using System.Collections.Generic;
using FMOD.Studio;
using FMODUnity;
using Cysharp.Threading.Tasks;
using System.Threading;
using System;
using BTR.Audio;
using Stylo.Epoch;

/// <summary>
/// Enhanced AudioManager for BTR project.
/// Phase 2 Enhancement: Added dynamic pool sizing and unified configuration support.
/// Maintains full backward compatibility with existing code.
/// </summary>
[DefaultExecutionOrder(-200)]
public class AudioManager : MonoBehaviour
{
    public static AudioManager Instance { get; private set; }

    [Header("FMOD Events")]
    [SerializeField]
    private EventReference musicEvent;

    [SerializeField]
    private StudioEventEmitter musicPlayback;

    [Header("Phase 2 Configuration (Optional)")]
    [SerializeField]
    [Tooltip("Optional unified audio configuration. If not assigned, uses default settings.")]
    private AudioConfigurationSO audioConfiguration;

    [Header("Dynamic Pool Settings")]
    [SerializeField] private bool enableDynamicPooling = false;
    [SerializeField] private bool enablePoolStatistics = false;
    [SerializeField] private bool enablePoolLogging = false;



    // Legacy pool system (maintained for backward compatibility)
    private Dictionary<string, Queue<EventInstance>> audioPool;

    // Enhanced pool system (Phase 2 addition)
    private Dictionary<string, AudioPoolInfo> enhancedAudioPools;

    private bool isMusicInitialized = false;
    public const int POOL_SIZE_PER_SOUND = 10; // Maintained for backward compatibility
    private readonly CancellationTokenSource _destroyToken = new();

    // Pool statistics (Phase 2 addition)
    private Dictionary<string, PoolStatistics> poolStats;

    // Epoch time integration (Phase 5 addition)
    private EpochTimeline timeline;

    private void Awake()
    {
        if (Instance != null && Instance != this)
        {
            Destroy(gameObject);
            return;
        }
        Instance = this;
        InitializeEpochIntegration();
        InitializeAudioSystem();
    }

    private async void Start()
    {
        await InitializeMusicPlaybackAsync().AttachExternalCancellation(_destroyToken.Token);
    }



    #region Phase 2 Data Structures

    /// <summary>
    /// Enhanced pool information for dynamic pool management.
    /// Phase 2 addition for better pool control and statistics.
    /// </summary>
    [Serializable]
    public class AudioPoolInfo
    {
        public Queue<EventInstance> instances = new Queue<EventInstance>();
        public int initialSize;
        public int maxSize;
        public int currentSize;
        public float lastAccessTime;
        public AudioConfigurationSO.AudioCategory category;

        public AudioPoolInfo(int initialSize, int maxSize, AudioConfigurationSO.AudioCategory category)
        {
            this.initialSize = initialSize;
            this.maxSize = maxSize;
            this.currentSize = 0;
            this.category = category;
            // Note: lastAccessTime will be set by AudioManager using GetCurrentTime()
            this.lastAccessTime = 0f;
        }
    }

    /// <summary>
    /// Pool statistics for monitoring and optimization.
    /// Phase 2 addition for performance tracking.
    /// </summary>
    [Serializable]
    public class PoolStatistics
    {
        public int totalRequests;
        public int poolHits;
        public int poolMisses;
        public int dynamicExpansions;
        public float averagePoolUtilization;
        public float lastUpdateTime;

        public float HitRate => totalRequests > 0 ? (float)poolHits / totalRequests : 0f;
        public float MissRate => totalRequests > 0 ? (float)poolMisses / totalRequests : 0f;
    }

    #endregion

    /// <summary>
    /// Initialize Epoch time integration for audio system.
    /// Phase 5 addition for time-aware audio management.
    /// </summary>
    private void InitializeEpochIntegration()
    {
        // Get or add EpochTimeline component
        timeline = GetComponent<EpochTimeline>();
        if (timeline == null)
        {
            timeline = gameObject.AddComponent<EpochTimeline>();
            timeline.globalClockKeyToUse = "Test"; // Match PlayerTimeControl clock key

            if (enablePoolLogging)
            {
                Debug.Log($"[{GetType().Name}] Added EpochTimeline component with clock key 'Test'");
            }
        }
        else
        {
            if (enablePoolLogging)
            {
                Debug.Log($"[{GetType().Name}] Using existing EpochTimeline component with clock key '{timeline.globalClockKeyToUse}'");
            }
        }
    }

    /// <summary>
    /// Get current time from Epoch timeline or fallback to Unity time.
    /// Phase 5 addition for consistent time handling across audio systems.
    /// </summary>
    private float GetCurrentTime()
    {
        return timeline != null ? timeline.time : Time.time;
    }

    /// <summary>
    /// Get current delta time from Epoch timeline or fallback to Unity delta time.
    /// Phase 5 addition for time-aware audio calculations.
    /// </summary>
    private float GetCurrentDeltaTime()
    {
        return timeline != null ? timeline.DeltaTime : Time.deltaTime;
    }

    private void InitializeAudioSystem()
    {
        // Initialize legacy pool system (backward compatibility)
        audioPool = new Dictionary<string, Queue<EventInstance>>();

        // Initialize enhanced pool system (Phase 2)
        enhancedAudioPools = new Dictionary<string, AudioPoolInfo>();
        poolStats = new Dictionary<string, PoolStatistics>();

        if (enablePoolLogging)
        {
            Debug.Log($"[{GetType().Name}] Audio system initialized with dynamic pooling: {enableDynamicPooling}");
        }
    }

    private async UniTask InitializeMusicPlaybackAsync(CancellationToken cancellationToken = default)
    {
        try
        {
            using var linkedCts = CancellationTokenSource.CreateLinkedTokenSource(cancellationToken, _destroyToken.Token);

            // Get the StudioEventEmitter from the same GameObject
            if (musicPlayback == null)
            {
                musicPlayback = GetComponent<StudioEventEmitter>();
                if (musicPlayback == null)
                {
                    Debug.LogError($"[{GetType().Name}] StudioEventEmitter not found on the FMOD music object! Please ensure it's properly set up.");
                    return;
                }
            }

            // If musicEvent is not assigned but the StudioEventEmitter has an event reference, use that
            if (musicEvent.IsNull && !musicPlayback.EventReference.IsNull)
            {
                musicEvent = musicPlayback.EventReference;
            }

            if (!musicEvent.IsNull)
            {
                // Only set the event reference if it's different from what's already set
                if (musicPlayback.EventReference.IsNull || musicPlayback.EventReference.Guid != musicEvent.Guid)
                {
                    musicPlayback.EventReference = musicEvent;
                }

                // Check if it's already playing
                PLAYBACK_STATE playbackState;
                musicPlayback.EventInstance.getPlaybackState(out playbackState);

                if (playbackState != PLAYBACK_STATE.PLAYING)
                {
                    musicPlayback.Play();
                    // Wait for playback to start
                    await UniTask.WaitUntil(() =>
                    {
                        musicPlayback.EventInstance.getPlaybackState(out playbackState);
                        return playbackState == PLAYBACK_STATE.PLAYING;
                    }, cancellationToken: linkedCts.Token);
                }

                isMusicInitialized = true;
            }
            else
            {
                Debug.LogWarning($"[{GetType().Name}] No music event assigned in AudioManager or StudioEventEmitter. Music functionality will be disabled.");
            }
        }
        catch (OperationCanceledException)
        {
            Debug.Log($"[{GetType().Name}] Music initialization cancelled");
            throw;
        }
        catch (Exception e)
        {
            Debug.LogError($"[{GetType().Name}] Error initializing music: {e.Message}");
            throw;
        }
    }

    #region Sound Effects Management

    // Enhanced Phase 5 methods with selective time scaling control

    /// <summary>
    /// Get or create FMOD EventInstance with selective time scaling control.
    /// Enhanced Phase 5 addition for selective audio time manipulation.
    /// </summary>
    public EventInstance GetOrCreateInstance(string eventPath, bool timeScaled = true, FMODEpochIntegration.AudioCategory category = FMODEpochIntegration.AudioCategory.Default)
    {
        if (!audioPool.ContainsKey(eventPath))
            audioPool[eventPath] = new Queue<EventInstance>();

        if (audioPool[eventPath].Count > 0)
        {
            var pooledInstance = audioPool[eventPath].Dequeue();

            // Register pooled instance with selective time scaling
            if (FMODEpochIntegration.Instance != null)
            {
                FMODEpochIntegration.Instance.RegisterInstance(pooledInstance, eventPath, timeScaled, category);
            }

            return pooledInstance;
        }

        var instance = FMODUnity.RuntimeManager.CreateInstance(eventPath);

        // Phase 5: Register with FMOD-Epoch integration with selective time scaling
        if (FMODEpochIntegration.Instance != null)
        {
            FMODEpochIntegration.Instance.RegisterInstance(instance, eventPath, timeScaled, category);
        }

        return instance;
    }

    public void ReleaseInstance(string eventPath, EventInstance instance)
    {
        if (!instance.isValid() || audioPool == null) return;

        if (!audioPool.ContainsKey(eventPath))
        {
            instance.stop(FMOD.Studio.STOP_MODE.IMMEDIATE);
            instance.release();
            return;
        }

        if (audioPool[eventPath].Count < POOL_SIZE_PER_SOUND)
        {
            instance.stop(FMOD.Studio.STOP_MODE.IMMEDIATE);
            instance.setVolume(0);
            audioPool[eventPath].Enqueue(instance);
        }
        else
        {
            instance.stop(FMOD.Studio.STOP_MODE.IMMEDIATE);
            instance.release();
        }
    }

    /// <summary>
    /// Get or create FMOD EventInstance asynchronously with selective time scaling control.
    /// Enhanced Phase 5 addition for selective audio time manipulation.
    /// </summary>
    public async UniTask<EventInstance> GetOrCreateInstanceAsync(string eventPath, bool timeScaled = true, FMODEpochIntegration.AudioCategory category = FMODEpochIntegration.AudioCategory.Default, CancellationToken cancellationToken = default)
    {
        try
        {
            using var linkedCts = CancellationTokenSource.CreateLinkedTokenSource(cancellationToken, _destroyToken.Token);

            if (!audioPool.ContainsKey(eventPath))
                audioPool[eventPath] = new Queue<EventInstance>();

            if (audioPool[eventPath].Count > 0)
            {
                var pooledInstance = audioPool[eventPath].Dequeue();

                // Register pooled instance with selective time scaling
                if (FMODEpochIntegration.Instance != null)
                {
                    FMODEpochIntegration.Instance.RegisterInstance(pooledInstance, eventPath, timeScaled, category);
                }

                return pooledInstance;
            }

            var instance = await UniTask.RunOnThreadPool(
                () => FMODUnity.RuntimeManager.CreateInstance(eventPath),
                cancellationToken: linkedCts.Token
            );

            // Phase 5: Register with FMOD-Epoch integration with selective time scaling
            if (FMODEpochIntegration.Instance != null)
            {
                FMODEpochIntegration.Instance.RegisterInstance(instance, eventPath, timeScaled, category);
            }

            return instance;
        }
        catch (Exception e)
        {
            Debug.LogError($"[{GetType().Name}] Error creating audio instance: {e.Message}");
            throw;
        }
    }

    public async UniTask ReleaseInstanceAsync(string eventPath, EventInstance instance, CancellationToken cancellationToken = default)
    {
        if (!instance.isValid() || audioPool == null) return;

        try
        {
            using var linkedCts = CancellationTokenSource.CreateLinkedTokenSource(cancellationToken, _destroyToken.Token);

            if (!audioPool.ContainsKey(eventPath))
            {
                await UniTask.RunOnThreadPool(() =>
                {
                    instance.stop(FMOD.Studio.STOP_MODE.IMMEDIATE);
                    instance.release();
                }, cancellationToken: linkedCts.Token);
                return;
            }

            if (audioPool[eventPath].Count < POOL_SIZE_PER_SOUND)
            {
                await UniTask.RunOnThreadPool(() =>
                {
                    instance.stop(FMOD.Studio.STOP_MODE.IMMEDIATE);
                    instance.setVolume(0);
                }, cancellationToken: linkedCts.Token);
                audioPool[eventPath].Enqueue(instance);
            }
            else
            {
                await UniTask.RunOnThreadPool(() =>
                {
                    instance.stop(FMOD.Studio.STOP_MODE.IMMEDIATE);
                    instance.release();
                }, cancellationToken: linkedCts.Token);
            }
        }
        catch (Exception e)
        {
            Debug.LogError($"[{GetType().Name}] Error releasing audio instance: {e.Message}");
            throw;
        }
    }

    #region Selective Time Scaling Convenience Methods (Phase 5)

    /// <summary>
    /// Create a time-scaled FMOD instance (affected by bullet time, slow motion).
    /// Phase 5 convenience method for explicit time-scaled audio.
    /// </summary>
    public EventInstance GetTimeScaledInstance(string eventPath, FMODEpochIntegration.AudioCategory category = FMODEpochIntegration.AudioCategory.Combat)
    {
        return GetOrCreateInstance(eventPath, timeScaled: true, category: category);
    }

    /// <summary>
    /// Create a time-independent FMOD instance (always plays at normal speed).
    /// Phase 5 convenience method for UI sounds, music, etc.
    /// </summary>
    public EventInstance GetTimeIndependentInstance(string eventPath, FMODEpochIntegration.AudioCategory category = FMODEpochIntegration.AudioCategory.UI)
    {
        return GetOrCreateInstance(eventPath, timeScaled: false, category: category);
    }

    /// <summary>
    /// Create a time-scaled FMOD instance asynchronously.
    /// Phase 5 convenience method for explicit time-scaled audio.
    /// </summary>
    public async UniTask<EventInstance> GetTimeScaledInstanceAsync(string eventPath, FMODEpochIntegration.AudioCategory category = FMODEpochIntegration.AudioCategory.Combat, CancellationToken cancellationToken = default)
    {
        return await GetOrCreateInstanceAsync(eventPath, timeScaled: true, category: category, cancellationToken: cancellationToken);
    }

    /// <summary>
    /// Create a time-independent FMOD instance asynchronously.
    /// Phase 5 convenience method for UI sounds, music, etc.
    /// </summary>
    public async UniTask<EventInstance> GetTimeIndependentInstanceAsync(string eventPath, FMODEpochIntegration.AudioCategory category = FMODEpochIntegration.AudioCategory.UI, CancellationToken cancellationToken = default)
    {
        return await GetOrCreateInstanceAsync(eventPath, timeScaled: false, category: category, cancellationToken: cancellationToken);
    }

    /// <summary>
    /// Enable or disable time scaling for a specific FMOD instance at runtime.
    /// Phase 5 addition for dynamic time scaling control.
    /// </summary>
    public bool SetInstanceTimeScaling(EventInstance instance, bool enableTimeScaling)
    {
        if (FMODEpochIntegration.Instance != null)
        {
            return FMODEpochIntegration.Instance.SetInstanceTimeScaling(instance, enableTimeScaling);
        }
        return false;
    }

    /// <summary>
    /// Get the current time scaling state of a specific FMOD instance.
    /// Phase 5 addition for runtime querying.
    /// </summary>
    public bool GetInstanceTimeScaling(EventInstance instance)
    {
        if (FMODEpochIntegration.Instance != null)
        {
            return FMODEpochIntegration.Instance.GetInstanceTimeScaling(instance);
        }
        return false;
    }

    #endregion
    #endregion

    #region Music Management (Phase 3 Enhanced)

    /// <summary>
    /// Set music parameter with enhanced error handling and validation.
    /// Phase 3 Enhancement: Improved error handling and configuration support.
    /// Backward compatibility method maintained.
    /// </summary>
    public void SetMusicParameter(string parameterName, float value)
    {
        try
        {
            if (string.IsNullOrEmpty(parameterName))
            {
                Debug.LogError($"[{GetType().Name}] Parameter name cannot be null or empty");
                return;
            }

            if (musicPlayback == null || !musicPlayback.EventInstance.isValid())
            {
                Debug.LogError($"[{GetType().Name}] Failed to set music parameter '{parameterName}': Invalid event instance");
                return;
            }

            // Enhanced validation using configuration if available
            if (audioConfiguration != null && audioConfiguration.Validation.enableParameterValidation)
            {
                if (!IsValidMusicParameter(parameterName))
                {
                    Debug.LogWarning($"[{GetType().Name}] Parameter '{parameterName}' may not exist in FMOD Studio project");
                }
            }

            PLAYBACK_STATE playbackState;
            musicPlayback.EventInstance.getPlaybackState(out playbackState);

            if (playbackState != PLAYBACK_STATE.PLAYING)
            {
                Debug.LogWarning($"[{GetType().Name}] FMOD event is not playing. Starting playback.");
                musicPlayback.Play();
            }

            FMOD.RESULT result = musicPlayback.EventInstance.setParameterByName(parameterName, value);
            if (result != FMOD.RESULT.OK)
            {
                Debug.LogError($"[{GetType().Name}] Failed to set music parameter '{parameterName}': {result}");
            }
            else if (enablePoolLogging)
            {
                Debug.Log($"[{GetType().Name}] Set music parameter '{parameterName}' to {value}");
            }
        }
        catch (Exception e)
        {
            Debug.LogError($"[{GetType().Name}] Exception setting music parameter '{parameterName}': {e.Message}");
        }
    }

    /// <summary>
    /// Validate if a music parameter exists in the current music event.
    /// Phase 3 addition for enhanced parameter validation.
    /// </summary>
    private bool IsValidMusicParameter(string parameterName)
    {
        try
        {
            if (musicPlayback == null || !musicPlayback.EventInstance.isValid())
                return false;

            // Try to get the parameter to check if it exists
            FMOD.RESULT result = musicPlayback.EventInstance.getParameterByName(parameterName, out float _);
            return result == FMOD.RESULT.OK;
        }
        catch
        {
            return false;
        }
    }

    private const string LOG_TAG_AUDIO = "[AudioManager]";

    public async UniTask SetMusicParameterAsync(string parameterName, float value, CancellationToken cancellationToken = default)
    {
        Debug.Log($"{LOG_TAG_AUDIO} SetMusicParameterAsync START. Parameter: '{parameterName}', Value: {value}. Incoming CT Requested: {cancellationToken.IsCancellationRequested}, DestroyToken Requested: {_destroyToken.Token.IsCancellationRequested}");
        if (musicPlayback == null || !musicPlayback.EventInstance.isValid())
        {
            Debug.LogError($"[{GetType().Name}] Failed to set music parameter '{parameterName}': Invalid event instance.");
            Debug.Log($"{LOG_TAG_AUDIO} SetMusicParameterAsync END (invalid event instance). Parameter: '{parameterName}'.");
            return;
        }

        CancellationTokenSource linkedCts = null;
        try
        {
            linkedCts = CancellationTokenSource.CreateLinkedTokenSource(cancellationToken, _destroyToken.Token);
            Debug.Log($"{LOG_TAG_AUDIO} SetMusicParameterAsync: Created linked CTS for '{parameterName}'. Incoming CT Requested: {cancellationToken.IsCancellationRequested}, Linked CT Requested: {linkedCts.Token.IsCancellationRequested}, DestroyToken Requested: {_destroyToken.Token.IsCancellationRequested}");

            PLAYBACK_STATE playbackState;
            musicPlayback.EventInstance.getPlaybackState(out playbackState);

            if (playbackState != PLAYBACK_STATE.PLAYING)
            {
                Debug.LogWarning($"{LOG_TAG_AUDIO} SetMusicParameterAsync: FMOD event for '{parameterName}' not playing (State: {playbackState}). Attempting start. Linked CT Requested: {linkedCts.Token.IsCancellationRequested}");
                musicPlayback.Play();

                Debug.Log($"{LOG_TAG_AUDIO} SetMusicParameterAsync: Waiting for FMOD event '{parameterName}' to start. Linked CT Requested: {linkedCts.Token.IsCancellationRequested}");
                await UniTask.WaitUntil(() =>
                {
                    musicPlayback.EventInstance.getPlaybackState(out playbackState);
                    if (linkedCts.Token.IsCancellationRequested) return true;
                    return playbackState == PLAYBACK_STATE.PLAYING;
                }, PlayerLoopTiming.Update, linkedCts.Token);

                if (linkedCts.Token.IsCancellationRequested)
                {
                    Debug.LogWarning($"{LOG_TAG_AUDIO} SetMusicParameterAsync: Cancelled while waiting for FMOD event '{parameterName}' to start. Linked CT Requested: {linkedCts.Token.IsCancellationRequested}");
                }
                musicPlayback.EventInstance.getPlaybackState(out playbackState);
                Debug.Log($"{LOG_TAG_AUDIO} SetMusicParameterAsync: FMOD event '{parameterName}' current state after WaitUntil: {playbackState}. Linked CT Requested: {linkedCts.Token.IsCancellationRequested}");
            }

            Debug.Log($"{LOG_TAG_AUDIO} SetMusicParameterAsync: Preparing RunOnThreadPool for '{parameterName}'. Linked CT Requested: {linkedCts.Token.IsCancellationRequested}");
            await UniTask.RunOnThreadPool(() =>
            {
                if (linkedCts.Token.IsCancellationRequested)
                {
                    Debug.LogWarning($"{LOG_TAG_AUDIO} SetMusicParameterAsync (ThreadPool): Task for '{parameterName}' cancelled before FMOD call. Linked CT Requested: {linkedCts.Token.IsCancellationRequested}");
                    return;
                }
                Debug.Log($"{LOG_TAG_AUDIO} SetMusicParameterAsync (ThreadPool): Setting FMOD param '{parameterName}' to {value}. Linked CT Requested: {linkedCts.Token.IsCancellationRequested}");
                FMOD.RESULT result = musicPlayback.EventInstance.setParameterByName(parameterName, value);
                Debug.Log($"{LOG_TAG_AUDIO} SetMusicParameterAsync (ThreadPool): FMOD param '{parameterName}' set, result: {result}. Linked CT Requested: {linkedCts.Token.IsCancellationRequested}");
                if (result != FMOD.RESULT.OK)
                {
                    Debug.LogError($"[{GetType().Name}] (ThreadPool) Failed to set music parameter '{parameterName}' to {value}: {result}");
                }
            }, cancellationToken: linkedCts.Token);

            Debug.Log($"{LOG_TAG_AUDIO} SetMusicParameterAsync: RunOnThreadPool for '{parameterName}' completed or scheduled. Linked CT Requested: {linkedCts.Token.IsCancellationRequested}");
        }
        catch (OperationCanceledException ex)
        {
            Debug.LogWarning($"{LOG_TAG_AUDIO} SetMusicParameterAsync: OperationCanceledException for '{parameterName}'. Message: {ex.Message}. Incoming CT Requested: {cancellationToken.IsCancellationRequested}, Linked CT Requested: {linkedCts?.Token.IsCancellationRequested ?? false}, DestroyToken Requested: {_destroyToken.Token.IsCancellationRequested}", this.gameObject);
            throw;
        }
        catch (Exception e)
        {
            Debug.LogError($"{LOG_TAG_AUDIO} SetMusicParameterAsync: General Exception for '{parameterName}'. Message: {e.Message}. Stack: {e.StackTrace}. Incoming CT Requested: {cancellationToken.IsCancellationRequested}, Linked CT Requested: {linkedCts?.Token.IsCancellationRequested ?? false}, DestroyToken Requested: {_destroyToken.Token.IsCancellationRequested}", this.gameObject);
            throw;
        }
        finally
        {
            linkedCts?.Dispose();
            Debug.Log($"{LOG_TAG_AUDIO} SetMusicParameterAsync END. Parameter: '{parameterName}'. Incoming CT Requested: {cancellationToken.IsCancellationRequested}, DestroyToken Requested: {_destroyToken.Token.IsCancellationRequested}");
        }
    }

    public async UniTask ApplyMusicChangesAsync(SceneGroup currentGroup, int currentScene, float currentSongSection, CancellationToken cancellationToken = default)
    {
        if (musicPlayback != null && currentGroup != null && currentScene < currentGroup.scenes.Length)
        {
            await SetMusicParameterAsync("Sections", currentSongSection, cancellationToken);
        }
    }

    /// <summary>
    /// Change music section by name with enhanced error handling and validation.
    /// Phase 3 Enhancement: Improved error handling and async support.
    /// </summary>
    public async UniTask ChangeMusicSectionByNameAsync(string sectionName, SceneGroup currentGroup, CancellationToken cancellationToken = default)
    {
        try
        {
            if (string.IsNullOrEmpty(sectionName))
            {
                Debug.LogError($"[{GetType().Name}] Section name cannot be null or empty");
                return;
            }

            if (currentGroup == null)
            {
                Debug.LogError($"[{GetType().Name}] SceneGroup cannot be null");
                return;
            }

            if (currentGroup.scenes == null || currentGroup.scenes.Length == 0)
            {
                Debug.LogWarning($"[{GetType().Name}] SceneGroup has no scenes");
                return;
            }

            using var linkedCts = CancellationTokenSource.CreateLinkedTokenSource(cancellationToken, _destroyToken.Token);

            for (int i = 0; i < currentGroup.scenes.Length; i++)
            {
                if (currentGroup.scenes[i].songSections == null) continue;

                for (int j = 0; j < currentGroup.scenes[i].songSections.Length; j++)
                {
                    if (currentGroup.scenes[i].songSections[j].name == sectionName)
                    {
                        await ChangeSongSectionAsync(currentGroup, i, currentGroup.scenes[i].songSections[j].section, linkedCts.Token);
                        return;
                    }
                }
            }

            Debug.LogWarning($"[{GetType().Name}] Section '{sectionName}' not found in the current group.");
        }
        catch (OperationCanceledException)
        {
            Debug.Log($"[{GetType().Name}] Music section change by name cancelled for '{sectionName}'");
            throw;
        }
        catch (Exception e)
        {
            Debug.LogError($"[{GetType().Name}] Error changing music section by name '{sectionName}': {e.Message}");
            throw;
        }
    }

    /// <summary>
    /// Change music section by name (synchronous version for backward compatibility).
    /// Phase 3 Enhancement: Added missing method from MusicManager consolidation.
    /// </summary>
    public void ChangeMusicSectionByName(string sectionName, SceneGroup currentGroup)
    {
        _ = ChangeMusicSectionByNameAsync(sectionName, currentGroup)
            .AttachExternalCancellation(_destroyToken.Token);
    }

    public async UniTask ChangeSongSectionAsync(SceneGroup currentGroup, int currentScene, float currentSongSection, CancellationToken cancellationToken = default)
    {
        if (musicPlayback == null || currentGroup == null ||
            currentGroup.scenes == null || currentScene >= currentGroup.scenes.Length)
        {
            Debug.LogWarning($"[{GetType().Name}] Invalid parameters in ChangeSongSection.");
            return;
        }

        try
        {
            using var linkedCts = CancellationTokenSource.CreateLinkedTokenSource(cancellationToken, _destroyToken.Token);

            var songSections = currentGroup.scenes[currentScene].songSections;
            int sectionIndex = Array.FindIndex(
                songSections,
                section => section.section == currentSongSection
            );

            if (sectionIndex == -1)
            {
                Debug.LogWarning($"[{GetType().Name}] Could not find section with value {currentSongSection} in scene {currentScene}");
                return;
            }

            await SetMusicParameterAsync("Sections", currentSongSection, linkedCts.Token);
            Debug.Log($"[{GetType().Name}] Song section changed to: {songSections[sectionIndex].name} (Section value: {currentSongSection})");
        }
        catch (OperationCanceledException)
        {
            Debug.Log($"[{GetType().Name}] Song section change cancelled");
            throw;
        }
        catch (Exception e)
        {
            Debug.LogError($"[{GetType().Name}] Error changing song section: {e.Message}");
            throw;
        }
    }

    // Keep non-async versions for compatibility
    public void ChangeSongSection(SceneGroup currentGroup, int currentScene, float currentSongSection)
    {
        _ = ChangeSongSectionAsync(currentGroup, currentScene, currentSongSection)
            .AttachExternalCancellation(_destroyToken.Token);
    }

    public void ApplyMusicChanges(SceneGroup currentGroup, int currentScene, float currentSongSection)
    {
        _ = ApplyMusicChangesAsync(currentGroup, currentScene, currentSongSection)
            .AttachExternalCancellation(_destroyToken.Token);
    }
    #endregion

    #region Phase 2 Enhanced Pool Management

    /// <summary>
    /// Get pool size for an event path using configuration or defaults.
    /// Phase 2 enhancement for dynamic pool sizing.
    /// </summary>
    private int GetPoolSizeForEvent(string eventPath, AudioConfigurationSO.AudioCategory category = AudioConfigurationSO.AudioCategory.Default)
    {
        if (audioConfiguration != null)
        {
            return audioConfiguration.GetPoolSizeForCategory(category);
        }

        // Fallback to legacy constant
        return POOL_SIZE_PER_SOUND;
    }

    /// <summary>
    /// Get or create enhanced audio pool with dynamic sizing.
    /// Phase 2 addition that works alongside legacy system.
    /// </summary>
    private AudioPoolInfo GetOrCreateEnhancedPool(string eventPath, AudioConfigurationSO.AudioCategory category = AudioConfigurationSO.AudioCategory.Default)
    {
        if (!enhancedAudioPools.ContainsKey(eventPath))
        {
            int initialSize = GetPoolSizeForEvent(eventPath, category);
            int maxSize = audioConfiguration?.Pooling.maxPoolSize ?? (initialSize * 2);

            var poolInfo = new AudioPoolInfo(initialSize, maxSize, category);
            enhancedAudioPools[eventPath] = poolInfo;

            if (enablePoolStatistics)
            {
                poolStats[eventPath] = new PoolStatistics();
            }

            if (enablePoolLogging)
            {
                Debug.Log($"[{GetType().Name}] Created enhanced pool for '{eventPath}' - Initial: {initialSize}, Max: {maxSize}");
            }
        }

        return enhancedAudioPools[eventPath];
    }

    /// <summary>
    /// Enhanced async instance creation with dynamic pool management.
    /// Phase 2 enhancement that maintains backward compatibility.
    /// </summary>
    public async UniTask<EventInstance> GetOrCreateInstanceEnhancedAsync(string eventPath, AudioConfigurationSO.AudioCategory category = AudioConfigurationSO.AudioCategory.Default, CancellationToken cancellationToken = default)
    {
        try
        {
            using var linkedCts = CancellationTokenSource.CreateLinkedTokenSource(cancellationToken, _destroyToken.Token);

            if (enableDynamicPooling)
            {
                return await GetInstanceFromEnhancedPool(eventPath, category, linkedCts.Token);
            }
            else
            {
                // Fallback to legacy system with category-based time scaling
                return await GetOrCreateInstanceAsync(eventPath, timeScaled: true, category: (FMODEpochIntegration.AudioCategory)category, cancellationToken: linkedCts.Token);
            }
        }
        catch (Exception e)
        {
            Debug.LogError($"[{GetType().Name}] Error creating enhanced audio instance: {e.Message}");
            throw;
        }
    }

    /// <summary>
    /// Get instance from enhanced pool with dynamic expansion.
    /// Phase 2 addition for improved pool management.
    /// </summary>
    private async UniTask<EventInstance> GetInstanceFromEnhancedPool(string eventPath, AudioConfigurationSO.AudioCategory category, CancellationToken cancellationToken)
    {
        var poolInfo = GetOrCreateEnhancedPool(eventPath, category);
        poolInfo.lastAccessTime = GetCurrentTime();

        // Update statistics
        if (enablePoolStatistics && poolStats.ContainsKey(eventPath))
        {
            poolStats[eventPath].totalRequests++;
        }

        // Try to get from existing pool
        if (poolInfo.instances.Count > 0)
        {
            if (enablePoolStatistics && poolStats.ContainsKey(eventPath))
            {
                poolStats[eventPath].poolHits++;
            }

            return poolInfo.instances.Dequeue();
        }

        // Pool is empty - create new instance
        if (enablePoolStatistics && poolStats.ContainsKey(eventPath))
        {
            poolStats[eventPath].poolMisses++;
        }

        // Check if we can expand the pool
        if (poolInfo.currentSize < poolInfo.maxSize)
        {
            if (enablePoolStatistics && poolStats.ContainsKey(eventPath))
            {
                poolStats[eventPath].dynamicExpansions++;
            }

            poolInfo.currentSize++;

            if (enablePoolLogging)
            {
                Debug.Log($"[{GetType().Name}] Expanding pool for '{eventPath}' to size {poolInfo.currentSize}");
            }
        }

        var instance = await UniTask.RunOnThreadPool(
            () => FMODUnity.RuntimeManager.CreateInstance(eventPath),
            cancellationToken: cancellationToken
        );

        // Phase 5: Register with FMOD-Epoch integration using category-based time scaling
        if (FMODEpochIntegration.Instance != null)
        {
            FMODEpochIntegration.Instance.RegisterInstanceWithCategory(instance, eventPath, (FMODEpochIntegration.AudioCategory)category);
        }

        return instance;
    }

    /// <summary>
    /// Enhanced async instance release with dynamic pool management.
    /// Phase 2 enhancement that maintains backward compatibility.
    /// </summary>
    public async UniTask ReleaseInstanceEnhancedAsync(string eventPath, EventInstance instance, AudioConfigurationSO.AudioCategory category = AudioConfigurationSO.AudioCategory.Default, CancellationToken cancellationToken = default)
    {
        try
        {
            using var linkedCts = CancellationTokenSource.CreateLinkedTokenSource(cancellationToken, _destroyToken.Token);

            if (enableDynamicPooling && enhancedAudioPools.ContainsKey(eventPath))
            {
                await ReleaseInstanceToEnhancedPool(eventPath, instance, category, linkedCts.Token);
            }
            else
            {
                // Fallback to legacy system
                await ReleaseInstanceAsync(eventPath, instance, cancellationToken: linkedCts.Token);
            }
        }
        catch (Exception e)
        {
            Debug.LogError($"[{GetType().Name}] Error releasing enhanced audio instance: {e.Message}");
            throw;
        }
    }

    /// <summary>
    /// Release instance to enhanced pool with size management.
    /// Phase 2 addition for improved pool management.
    /// </summary>
    private async UniTask ReleaseInstanceToEnhancedPool(string eventPath, EventInstance instance, AudioConfigurationSO.AudioCategory category, CancellationToken cancellationToken)
    {
        var poolInfo = GetOrCreateEnhancedPool(eventPath, category);

        if (poolInfo.instances.Count < poolInfo.maxSize)
        {
            await UniTask.RunOnThreadPool(() =>
            {
                instance.stop(FMOD.Studio.STOP_MODE.IMMEDIATE);
                instance.setVolume(0);
            }, cancellationToken: cancellationToken);

            poolInfo.instances.Enqueue(instance);
        }
        else
        {
            // Pool is full, release the instance
            await UniTask.RunOnThreadPool(() =>
            {
                instance.stop(FMOD.Studio.STOP_MODE.IMMEDIATE);
                instance.release();
            }, cancellationToken: cancellationToken);

            poolInfo.currentSize--;
        }
    }

    /// <summary>
    /// Get pool statistics for monitoring and debugging.
    /// Phase 2 addition for performance analysis.
    /// </summary>
    public Dictionary<string, PoolStatistics> GetPoolStatistics()
    {
        if (!enablePoolStatistics)
        {
            Debug.LogWarning($"[{GetType().Name}] Pool statistics are disabled. Enable them in the inspector to collect data.");
            return new Dictionary<string, PoolStatistics>();
        }

        // Update utilization statistics
        foreach (var kvp in enhancedAudioPools)
        {
            if (poolStats.ContainsKey(kvp.Key))
            {
                var poolInfo = kvp.Value;
                var stats = poolStats[kvp.Key];

                if (poolInfo.currentSize > 0)
                {
                    stats.averagePoolUtilization = (float)poolInfo.instances.Count / poolInfo.currentSize;
                }

                stats.lastUpdateTime = GetCurrentTime();
            }
        }

        return new Dictionary<string, PoolStatistics>(poolStats);
    }

    /// <summary>
    /// Configure dynamic pooling at runtime.
    /// Phase 2 addition for runtime configuration.
    /// </summary>
    public void ConfigureDynamicPooling(bool enabled, bool statistics = false, bool logging = false)
    {
        enableDynamicPooling = enabled;
        enablePoolStatistics = statistics;
        enablePoolLogging = logging;

        Debug.Log($"[{GetType().Name}] Dynamic pooling configured - Enabled: {enabled}, Statistics: {statistics}, Logging: {logging}");
    }

    /// <summary>
    /// Set audio configuration at runtime.
    /// Phase 2 addition for runtime configuration changes.
    /// </summary>
    public void SetAudioConfiguration(AudioConfigurationSO configuration)
    {
        audioConfiguration = configuration;

        if (enablePoolLogging)
        {
            Debug.Log($"[{GetType().Name}] Audio configuration updated: {(configuration != null ? configuration.name : "null")}");
        }
    }



    #endregion



    private void OnDestroy()
    {
        _destroyToken.Cancel();
        _destroyToken.Dispose();

        // Cleanup legacy pools
        if (audioPool != null)
        {
            foreach (var pool in audioPool.Values)
            {
                while (pool.Count > 0)
                {
                    var instance = pool.Dequeue();
                    if (instance.isValid())
                        instance.release();
                }
            }
            audioPool.Clear();
        }

        // Cleanup enhanced pools (Phase 2 addition)
        if (enhancedAudioPools != null)
        {
            foreach (var poolInfo in enhancedAudioPools.Values)
            {
                while (poolInfo.instances.Count > 0)
                {
                    var instance = poolInfo.instances.Dequeue();
                    if (instance.isValid())
                        instance.release();
                }
            }
            enhancedAudioPools.Clear();
        }
    }
}
